"use client";

import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Eye, Users, Building2, Shield } from 'lucide-react';
import { useAuth } from '@/lib/auth/context';
import { getRoleDisplayName } from '@/lib/rbac/permissions';

export function ScopeIndicator() {
  const { user, currentScope } = useAuth();

  if (!user) return null;

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center space-x-2">
          <Eye className="h-5 w-5" />
          <span>Current Data Scope</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="flex items-center space-x-2">
            <Building2 className="h-4 w-4 text-muted-foreground" />
            <div>
              <div className="text-sm font-medium">{currentScope.entityName}</div>
              <div className="text-xs text-muted-foreground">Current Entity</div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Shield className="h-4 w-4 text-muted-foreground" />
            <div>
              <Badge variant="outline">{getRoleDisplayName(currentScope.level)}</Badge>
              <div className="text-xs text-muted-foreground mt-1">Access Level</div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <div>
              <div className="text-sm font-medium">{currentScope.accessibleEntityIds.length}</div>
              <div className="text-xs text-muted-foreground">Accessible Entities</div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <div className={`h-3 w-3 rounded-full ${currentScope.canViewChildren ? 'bg-green-500' : 'bg-yellow-500'}`} />
            <div>
              <div className="text-sm font-medium">
                {currentScope.canViewChildren ? 'Hierarchical' : 'Own Only'}
              </div>
              <div className="text-xs text-muted-foreground">View Scope</div>
            </div>
          </div>
        </div>
        
        {currentScope.accessibleEntityIds.length > 1 && (
          <div className="mt-4 pt-4 border-t">
            <div className="text-sm font-medium mb-2">Accessible Entity IDs:</div>
            <div className="flex flex-wrap gap-1">
              {currentScope.accessibleEntityIds.map((entityId) => (
                <Badge key={entityId} variant="secondary" className="text-xs">
                  {entityId}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
