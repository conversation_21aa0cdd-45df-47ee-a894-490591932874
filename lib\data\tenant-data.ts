// Enhanced data structure with tenant/entity associations

export interface TenantCustomer {
  id: number;
  name: string;
  email: string;
  totalSpend: number;
  lastTransaction: string;
  transactionCount: number;
  avgTransactionValue: number;
  customerSince: string;
  segment: string;
  status: string;
  // Tenant associations
  entityId: string;
  agentId?: string;
  merchantId?: string;
  merchantAdminId?: string;
  branchAdminId?: string;
  tellerId?: string;
}

export interface TenantTransaction {
  id: string;
  customerId: number;
  amount: number;
  date: string;
  type: string;
  status: string;
  // Tenant associations
  entityId: string;
  agentId?: string;
  merchantId?: string;
  merchantAdminId?: string;
  branchAdminId?: string;
  tellerId?: string;
}

export interface TenantAlert {
  id: number;
  type: string;
  customer: string;
  description: string;
  date: string;
  severity: string;
  // Tenant associations
  entityId: string;
  agentId?: string;
  merchantId?: string;
  merchantAdminId?: string;
  branchAdminId?: string;
  tellerId?: string;
}

// Mock tenant-aware customer data
export const tenantCustomers: TenantCustomer[] = [
  // Agent level data
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    totalSpend: 2450.75,
    lastTransaction: "2023-05-15",
    transactionCount: 12,
    avgTransactionValue: 204.23,
    customerSince: "2022-01-10",
    segment: "High Value",
    status: "Active",
    entityId: "teller-1",
    agentId: "agent-1",
    merchantId: "merchant-1",
    merchantAdminId: "merchant-admin-1",
    branchAdminId: "branch-admin-1",
    tellerId: "teller-1",
  },
  {
    id: 2,
    name: "Sarah Johnson",
    email: "<EMAIL>",
    totalSpend: 1875.50,
    lastTransaction: "2023-05-18",
    transactionCount: 8,
    avgTransactionValue: 234.44,
    customerSince: "2022-03-22",
    segment: "Regular",
    status: "Active",
    entityId: "teller-2",
    agentId: "agent-1",
    merchantId: "merchant-1",
    merchantAdminId: "merchant-admin-1",
    branchAdminId: "branch-admin-1",
    tellerId: "teller-2",
  },
  {
    id: 3,
    name: "Michael Brown",
    email: "<EMAIL>",
    totalSpend: 3200.25,
    lastTransaction: "2023-05-10",
    transactionCount: 15,
    avgTransactionValue: 213.35,
    customerSince: "2021-11-05",
    segment: "High Value",
    status: "Active",
    entityId: "teller-3",
    agentId: "agent-1",
    merchantId: "merchant-1",
    merchantAdminId: "merchant-admin-2",
    branchAdminId: "branch-admin-2",
    tellerId: "teller-3",
  },
  {
    id: 4,
    name: "Emily Davis",
    email: "<EMAIL>",
    totalSpend: 950.30,
    lastTransaction: "2023-04-28",
    transactionCount: 5,
    avgTransactionValue: 190.06,
    customerSince: "2022-06-15",
    segment: "New",
    status: "At Risk",
    entityId: "teller-4",
    agentId: "agent-1",
    merchantId: "merchant-2",
    merchantAdminId: "merchant-admin-3",
    branchAdminId: "branch-admin-3",
    tellerId: "teller-4",
  },
  {
    id: 5,
    name: "David Wilson",
    email: "<EMAIL>",
    totalSpend: 4150.90,
    lastTransaction: "2023-05-20",
    transactionCount: 18,
    avgTransactionValue: 230.61,
    customerSince: "2021-08-12",
    segment: "High Value",
    status: "Active",
    entityId: "teller-1",
    agentId: "agent-1",
    merchantId: "merchant-1",
    merchantAdminId: "merchant-admin-1",
    branchAdminId: "branch-admin-1",
    tellerId: "teller-1",
  },
];

// Mock tenant-aware alerts data
export const tenantAlerts: TenantAlert[] = [
  {
    id: 1,
    type: "Unusual Activity",
    customer: "John Smith",
    description: "Sudden increase in transaction frequency",
    date: "2023-05-20",
    severity: "medium",
    entityId: "teller-1",
    agentId: "agent-1",
    merchantId: "merchant-1",
    merchantAdminId: "merchant-admin-1",
    branchAdminId: "branch-admin-1",
    tellerId: "teller-1",
  },
  {
    id: 2,
    type: "High Value Transaction",
    customer: "David Wilson",
    description: "Transaction value exceeds 3x average",
    date: "2023-05-19",
    severity: "high",
    entityId: "teller-1",
    agentId: "agent-1",
    merchantId: "merchant-1",
    merchantAdminId: "merchant-admin-1",
    branchAdminId: "branch-admin-1",
    tellerId: "teller-1",
  },
  {
    id: 3,
    type: "Churn Risk",
    customer: "Emily Davis",
    description: "No activity in last 30 days",
    date: "2023-05-18",
    severity: "medium",
    entityId: "teller-4",
    agentId: "agent-1",
    merchantId: "merchant-2",
    merchantAdminId: "merchant-admin-3",
    branchAdminId: "branch-admin-3",
    tellerId: "teller-4",
  },
  {
    id: 4,
    type: "Fraud Suspicion",
    customer: "Anonymous User",
    description: "Multiple failed payment attempts",
    date: "2023-05-17",
    severity: "high",
    entityId: "teller-2",
    agentId: "agent-1",
    merchantId: "merchant-1",
    merchantAdminId: "merchant-admin-1",
    branchAdminId: "branch-admin-1",
    tellerId: "teller-2",
  },
];

// Aggregated metrics by entity
export const entityMetrics = {
  "agent-1": {
    totalCustomers: 1245,
    totalRevenue: 485740,
    avgTransactionValue: 224.50,
    transactionVolume: 2160,
    activeAlerts: 15,
  },
  "merchant-1": {
    totalCustomers: 856,
    totalRevenue: 342180,
    avgTransactionValue: 218.30,
    transactionVolume: 1568,
    activeAlerts: 8,
  },
  "merchant-2": {
    totalCustomers: 389,
    totalRevenue: 143560,
    avgTransactionValue: 235.20,
    transactionVolume: 592,
    activeAlerts: 7,
  },
  "merchant-admin-1": {
    totalCustomers: 445,
    totalRevenue: 178920,
    avgTransactionValue: 215.80,
    transactionVolume: 829,
    activeAlerts: 4,
  },
  "merchant-admin-2": {
    totalCustomers: 411,
    totalRevenue: 163260,
    avgTransactionValue: 220.90,
    transactionVolume: 739,
    activeAlerts: 4,
  },
  "branch-admin-1": {
    totalCustomers: 223,
    totalRevenue: 89460,
    avgTransactionValue: 212.40,
    transactionVolume: 421,
    activeAlerts: 2,
  },
  "branch-admin-2": {
    totalCustomers: 222,
    totalRevenue: 89460,
    avgTransactionValue: 219.20,
    transactionVolume: 408,
    activeAlerts: 2,
  },
  "teller-1": {
    totalCustomers: 112,
    totalRevenue: 44730,
    avgTransactionValue: 208.60,
    transactionVolume: 214,
    activeAlerts: 1,
  },
  "teller-2": {
    totalCustomers: 111,
    totalRevenue: 44730,
    avgTransactionValue: 216.20,
    transactionVolume: 207,
    activeAlerts: 1,
  },
};

// Chart data with entity associations
export const tenantTransactionVolumeData = [
  { month: "Jan", volume: 145, entityId: "agent-1" },
  { month: "Feb", volume: 165, entityId: "agent-1" },
  { month: "Mar", volume: 180, entityId: "agent-1" },
  { month: "Apr", volume: 195, entityId: "agent-1" },
  { month: "May", volume: 210, entityId: "agent-1" },
  { month: "Jun", volume: 225, entityId: "agent-1" },
];

export const tenantAvgTransactionValueData = [
  { month: "Jan", value: 198.50, entityId: "agent-1" },
  { month: "Feb", value: 205.30, entityId: "agent-1" },
  { month: "Mar", value: 212.80, entityId: "agent-1" },
  { month: "Apr", value: 218.90, entityId: "agent-1" },
  { month: "May", value: 224.50, entityId: "agent-1" },
  { month: "Jun", value: 230.20, entityId: "agent-1" },
];

export const tenantCustomerSegmentationData = [
  { name: "High Value", value: 35, count: 436, entityId: "agent-1" },
  { name: "Regular", value: 45, count: 560, entityId: "agent-1" },
  { name: "New", value: 15, count: 187, entityId: "agent-1" },
  { name: "At Risk", value: 5, count: 62, entityId: "agent-1" },
];
