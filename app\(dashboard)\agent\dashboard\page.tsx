"use client";

import {
  Building2,
  TrendingUp,
  Users,
  DollarSign,
  Activity,
  AlertTriangle,
  BarChart3,
  Target
} from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Legend,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatCurrency } from "@/lib/utils/currency";

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

// Mock data for Agent dashboard
const merchantPerformanceData = [
  { name: "TechCorp Payments", transactions: 1250, revenue: 342180, score: 95 },
  { name: "RetailPay Solutions", transactions: 890, revenue: 143560, score: 87 },
  { name: "QuickPay Systems", transactions: 650, revenue: 98750, score: 78 },
  { name: "FastCash Ltd", transactions: 420, revenue: 67890, score: 65 },
];

const merchantActivationData = [
  { month: "Jan", active: 12, inactive: 3, new: 5 },
  { month: "Feb", active: 15, inactive: 2, new: 4 },
  { month: "Mar", active: 18, inactive: 1, new: 6 },
  { month: "Apr", active: 22, inactive: 2, new: 3 },
  { month: "May", active: 25, inactive: 1, new: 7 },
];

const transactionVolumeByMerchant = [
  { merchant: "TechCorp", volume: 1250, value: 342180 },
  { merchant: "RetailPay", volume: 890, value: 143560 },
  { merchant: "QuickPay", volume: 650, value: 98750 },
  { merchant: "FastCash", volume: 420, value: 67890 },
];

const branchActivityHeatmap = [
  { merchant: "TechCorp", branches: 8, avgTransactions: 156, totalValue: 42750 },
  { merchant: "RetailPay", branches: 5, avgTransactions: 178, totalValue: 28712 },
  { merchant: "QuickPay", branches: 3, avgTransactions: 217, totalValue: 32917 },
  { merchant: "FastCash", branches: 2, avgTransactions: 210, totalValue: 33945 },
];

const highValueTransactions = [
  { id: "TXN001", merchant: "TechCorp", amount: 15000, date: "2023-05-20", flag: "High" },
  { id: "TXN002", merchant: "RetailPay", amount: 12500, date: "2023-05-19", flag: "Medium" },
  { id: "TXN003", merchant: "QuickPay", amount: 18750, date: "2023-05-18", flag: "High" },
  { id: "TXN004", merchant: "FastCash", amount: 9800, date: "2023-05-17", flag: "Medium" },
];

export default function AgentDashboard() {
  const { user, currentScope } = useAuth();

  if (!user || user.role !== 'agent') {
    return <div>Access denied. Agent role required.</div>;
  }

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Agent Dashboard</h2>
        <p className="text-muted-foreground">
          Comprehensive view of all merchants, branches, and transactions
        </p>
      </div>

      <FilterBar />

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Active Merchants"
          value="25"
          icon={Building2}
          trend={{ value: 12, isPositive: true }}
        />
        <KpiCard
          title="Total Transaction Volume"
          value="3,210"
          icon={Activity}
          trend={{ value: 8.2, isPositive: true }}
        />
        <KpiCard
          title="Total Revenue"
          value={formatCurrency(652380)}
          icon={DollarSign}
          trend={{ value: 15.3, isPositive: true }}
        />
        <KpiCard
          title="Avg. Merchant Score"
          value="81.3"
          icon={Target}
          trend={{ value: 5.7, isPositive: true }}
        />
      </div>

      {/* Merchant Performance Ranking */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Merchant Performance Ranking">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={merchantPerformanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
              <YAxis />
              <Tooltip />
              <Bar dataKey="score" fill="#0088FE" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Merchant Activation Tracker">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={merchantActivationData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="active" stroke="#00C49F" name="Active" />
              <Line type="monotone" dataKey="inactive" stroke="#FF8042" name="Inactive" />
              <Line type="monotone" dataKey="new" stroke="#0088FE" name="New" />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Transaction Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Transaction Volume by Merchant">
          <ResponsiveContainer width="100%" height="100%">
            <ScatterChart data={transactionVolumeByMerchant}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="volume" name="Volume" />
              <YAxis dataKey="value" name="Value" />
              <Tooltip cursor={{ strokeDasharray: '3 3' }} />
              <Scatter dataKey="value" fill="#8884D8" />
            </ScatterChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Branch Activity Heatmap">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={branchActivityHeatmap}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="merchant" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="branches" fill="#0088FE" name="Branches" />
              <Bar dataKey="avgTransactions" fill="#00C49F" name="Avg Transactions" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Merchant Comparison Tool */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Merchant Comparison Tool</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {merchantPerformanceData.map((merchant, index) => (
              <div key={merchant.name} className="p-4 border rounded-lg">
                <div className="font-medium text-sm mb-2">{merchant.name}</div>
                <div className="space-y-2">
                  <div className="flex justify-between text-xs">
                    <span>Transactions:</span>
                    <span className="font-medium">{merchant.transactions}</span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span>Revenue:</span>
                    <span className="font-medium">{formatCurrency(merchant.revenue)}</span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span>Score:</span>
                    <Badge variant={merchant.score >= 90 ? "default" : merchant.score >= 80 ? "secondary" : "destructive"}>
                      {merchant.score}
                    </Badge>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* High-Value Transaction Flags */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5" />
            <span>High-Value Transaction Flags</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
           title="Transactions"
            columns={[
              { key: "id", title: "Transaction ID" },
              { key: "merchant", title: "Merchant" },
              { key: "amount", title: "Amount" },
              { key: "date", title: "Date" },
              { key: "flag", title: "Flag Level" },
            ]}
            data={highValueTransactions.map(txn => ({
              ...txn,
              amount: formatCurrency(txn.amount)
            }))}
          />
        </CardContent>
      </Card>
    </div>
  );
}
