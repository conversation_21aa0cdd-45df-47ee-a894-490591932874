export type UserRole = 'agent' | 'merchant' | 'merchant_admin' | 'branch_admin' | 'teller';

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  avatar?: string;
  // Hierarchical relationships
  agentId?: string;      // For merchants and below
  merchantId?: string;   // For merchant_admins and below
  merchantAdminId?: string; // For branch_admins and below
  branchAdminId?: string;   // For tellers
  // Direct children IDs for quick access
  childrenIds?: string[];
  // Tenant/Organization info
  organizationId: string;
  organizationName: string;
}

export interface Organization {
  id: string;
  name: string;
  type: 'agent' | 'merchant' | 'branch';
  parentId?: string;
  children?: Organization[];
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  currentScope: DataScope;
  error: AuthError | null;
  sessionExpiresAt: number | null;
}

export interface AuthError {
  code: string;
  message: string;
  details?: any;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}

export interface DataScope {
  level: UserRole;
  entityId: string;
  entityName: string;
  canViewChildren: boolean;
  accessibleEntityIds: string[];
}

export interface Permission {
  resource: string;
  action: 'read' | 'write' | 'delete' | 'admin';
  scope: 'own' | 'children' | 'all';
}

export const ROLE_HIERARCHY: Record<UserRole, number> = {
  agent: 5,
  merchant_admin: 4,
  merchant: 3,
  branch_admin: 2,
  teller: 1,
};

export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  agent: [
    { resource: 'dashboard', action: 'read', scope: 'all' },
    { resource: 'analytics', action: 'read', scope: 'all' },
    { resource: 'users', action: 'admin', scope: 'all' },
    { resource: 'merchant_admin', action: 'admin', scope: 'all' },
    { resource: 'merchant_comparison', action: 'read', scope: 'all' },
    { resource: 'merchant_activation', action: 'read', scope: 'all' },
  ],
  merchant: [
    { resource: 'dashboard', action: 'read', scope: 'children' },
    { resource: 'analytics', action: 'read', scope: 'children' },
    { resource: 'users', action: 'write', scope: 'children' },
  ],
  merchant_admin: [
    { resource: 'dashboard', action: 'read', scope: 'children' },
    { resource: 'analytics', action: 'read', scope: 'children' },
    { resource: 'users', action: 'write', scope: 'children' },
    { resource: 'branches', action: 'write', scope: 'children' },
    { resource: 'branch_performance', action: 'read', scope: 'children' },
    { resource: 'staff_efficiency', action: 'read', scope: 'children' },
    { resource: 'merchant', action: 'write', scope: 'children' },
  ],
  branch_admin: [
    { resource: 'dashboard', action: 'read', scope: 'children' },
    { resource: 'analytics', action: 'read', scope: 'children' },
    { resource: 'users', action: 'write', scope: 'children' },
    { resource: 'tellers', action: 'write', scope: 'children' },
    { resource: 'teller_performance', action: 'read', scope: 'children' },
    { resource: 'cash_flow', action: 'read', scope: 'children' },
  ],
  teller: [
    { resource: 'dashboard', action: 'read', scope: 'own' },
    { resource: 'analytics', action: 'read', scope: 'own' },
    { resource: 'transactions', action: 'write', scope: 'own' },
    { resource: 'personal_performance', action: 'read', scope: 'own' },
  ],
};
