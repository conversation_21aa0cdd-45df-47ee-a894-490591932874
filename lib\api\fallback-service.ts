import {
  MerchantAdminDashboard,
  CustomerAnalytics,
  BranchAnalytics,
  HighValueCustomer,
  BranchPerformance,
  TellerPerformance,
  CustomerSegmentAnalysis,
  SpendingTrendData,
  PaginatedResponse,
} from './types';

// Fallback service that provides mock data when API is unavailable
export class FallbackService {
  // Mock Dashboard Data
  static getMockDashboardData(): MerchantAdminDashboard {
    return {
      kpis: {
        totalBranches: 4,
        activeTellers: 16,
        totalTransactions: 1640,
        totalRevenue: 443000,
        trends: {
          branches: 0,
          tellers: 6.3,
          transactions: 12.5,
          revenue: 8.7,
        },
      },
      branchPerformance: this.getMockBranchPerformance(),
      tellerPerformance: this.getMockTellerPerformance(),
      customerBehavior: [
        { hour: "9AM", newCustomers: 12, returningCustomers: 45, avgTransaction: 245, totalTransactions: 57 },
        { hour: "11AM", newCustomers: 18, returningCustomers: 67, avgTransaction: 289, totalTransactions: 85 },
        { hour: "1PM", newCustomers: 25, returningCustomers: 89, avgTransaction: 312, totalTransactions: 114 },
        { hour: "3PM", newCustomers: 22, returningCustomers: 78, avgTransaction: 298, totalTransactions: 100 },
        { hour: "5PM", newCustomers: 15, returningCustomers: 56, avgTransaction: 267, totalTransactions: 71 },
      ],
      peakTransactionPeriods: [
        { day: "Monday", morning: 45, afternoon: 78, evening: 34, total: 157 },
        { day: "Tuesday", morning: 52, afternoon: 82, evening: 38, total: 172 },
        { day: "Wednesday", morning: 48, afternoon: 85, evening: 41, total: 174 },
        { day: "Thursday", morning: 55, afternoon: 92, evening: 45, total: 192 },
        { day: "Friday", morning: 62, afternoon: 105, evening: 67, total: 234 },
        { day: "Saturday", morning: 38, afternoon: 95, evening: 78, total: 211 },
        { day: "Sunday", morning: 25, afternoon: 67, evening: 45, total: 137 },
      ],
      customerSegmentation: [
        { name: "High Value", value: 35, count: 156, avgSpend: 450, percentage: 35 },
        { name: "Regular", value: 45, count: 203, avgSpend: 280, percentage: 45 },
        { name: "New", value: 15, count: 67, avgSpend: 180, percentage: 15 },
        { name: "At Risk", value: 5, count: 23, avgSpend: 120, percentage: 5 },
      ],
      topProducts: [
        { id: "1", product: "Mobile Top-up", sales: 1250, revenue: 62500, growth: 12.5, category: "Mobile Services" },
        { id: "2", product: "Bill Payment", sales: 890, revenue: 44500, growth: 8.3, category: "Utilities" },
        { id: "3", product: "Money Transfer", sales: 650, revenue: 32500, growth: 15.2, category: "Financial Services" },
        { id: "4", product: "Account Opening", sales: 420, revenue: 21000, growth: 5.7, category: "Banking" },
      ],
      highValueCustomers: this.getMockHighValueCustomers(),
    };
  }

  static getMockHighValueCustomers(): HighValueCustomer[] {
    return [
      {
        id: "1",
        name: "David Wilson",
        email: "<EMAIL>",
        phone: "+233 24 123 4567",
        totalSpend: 4150,
        transactions: 18,
        lastVisit: "2023-05-20",
        branchId: "branch-3",
        branchName: "Mall Branch",
        tellerId: "teller-7",
        tellerName: "Lisa Davis",
        avgTransactionValue: 230.56,
        customerSince: "2022-01-15",
        status: "active",
        segment: "High Value",
      },
      {
        id: "2",
        name: "Emma Thompson",
        email: "<EMAIL>",
        phone: "+233 24 234 5678",
        totalSpend: 3890,
        transactions: 15,
        lastVisit: "2023-05-19",
        branchId: "branch-1",
        branchName: "Downtown Branch",
        tellerId: "teller-1",
        tellerName: "John Smith",
        avgTransactionValue: 259.33,
        customerSince: "2022-03-10",
        status: "active",
        segment: "High Value",
      },
      {
        id: "3",
        name: "Robert Chen",
        email: "<EMAIL>",
        phone: "+233 24 345 6789",
        totalSpend: 3650,
        transactions: 12,
        lastVisit: "2023-05-18",
        branchId: "branch-2",
        branchName: "Uptown Branch",
        tellerId: "teller-3",
        tellerName: "Mike Brown",
        avgTransactionValue: 304.17,
        customerSince: "2021-11-22",
        status: "active",
        segment: "High Value",
      },
      {
        id: "4",
        name: "Maria Garcia",
        email: "<EMAIL>",
        phone: "+233 24 456 7890",
        totalSpend: 3420,
        transactions: 14,
        lastVisit: "2023-05-17",
        branchId: "branch-4",
        branchName: "Airport Branch",
        tellerId: "teller-9",
        tellerName: "James Wilson",
        avgTransactionValue: 244.29,
        customerSince: "2022-07-05",
        status: "active",
        segment: "High Value",
      },
    ];
  }

  static getMockBranchPerformance(): BranchPerformance[] {
    return [
      {
        id: "branch-1",
        name: "Downtown Branch",
        transactions: 450,
        revenue: 125000,
        score: 92,
        efficiency: 85,
        tellerCount: 4,
        customerCount: 156,
      },
      {
        id: "branch-2",
        name: "Uptown Branch",
        transactions: 380,
        revenue: 98000,
        score: 88,
        efficiency: 82,
        tellerCount: 4,
        customerCount: 134,
      },
      {
        id: "branch-3",
        name: "Mall Branch",
        transactions: 520,
        revenue: 142000,
        score: 95,
        efficiency: 90,
        tellerCount: 4,
        customerCount: 178,
      },
      {
        id: "branch-4",
        name: "Airport Branch",
        transactions: 290,
        revenue: 78000,
        score: 78,
        efficiency: 75,
        tellerCount: 4,
        customerCount: 98,
      },
    ];
  }

  static getMockTellerPerformance(): TellerPerformance[] {
    return [
      {
        id: "teller-1",
        name: "John Smith",
        branchId: "branch-1",
        branchName: "Downtown Branch",
        transactions: 89,
        avgValue: 278,
        score: 94,
        efficiency: 88,
        customersSaved: 45,
      },
      {
        id: "teller-2",
        name: "Sarah Johnson",
        branchId: "branch-1",
        branchName: "Downtown Branch",
        transactions: 76,
        avgValue: 312,
        score: 91,
        efficiency: 85,
        customersSaved: 38,
      },
      {
        id: "teller-3",
        name: "Mike Brown",
        branchId: "branch-2",
        branchName: "Uptown Branch",
        transactions: 82,
        avgValue: 258,
        score: 88,
        efficiency: 82,
        customersSaved: 41,
      },
      {
        id: "teller-4",
        name: "Lisa Davis",
        branchId: "branch-3",
        branchName: "Mall Branch",
        transactions: 95,
        avgValue: 273,
        score: 96,
        efficiency: 92,
        customersSaved: 52,
      },
    ];
  }

  static getMockCustomerAnalytics(): CustomerAnalytics {
    return {
      customers: this.getMockHighValueCustomers(),
      metrics: {
        totalCustomers: 566,
        highValueCount: 156,
        averageSpend: 2850,
        retentionRate: 87.5,
        growthRate: 12.3,
      },
      segmentAnalysis: [
        {
          segment: "High Value",
          count: 156,
          avgSpend: 3200,
          avgTransactionValue: 285,
          purchaseFrequency: 11.2,
          retentionRate: 92,
          growthRate: 8.5,
        },
        {
          segment: "Regular",
          count: 203,
          avgSpend: 1850,
          avgTransactionValue: 185,
          purchaseFrequency: 10,
          retentionRate: 78,
          growthRate: 15.2,
        },
        {
          segment: "Occasional",
          count: 140,
          avgSpend: 950,
          avgTransactionValue: 135,
          purchaseFrequency: 7,
          retentionRate: 65,
          growthRate: 22.1,
        },
        {
          segment: "New",
          count: 67,
          avgSpend: 450,
          avgTransactionValue: 150,
          purchaseFrequency: 3,
          retentionRate: 40,
          growthRate: 45.8,
        },
      ],
      spendingTrends: [
        { month: "Jan", totalSpend: 145000, avgSpend: 2100, customerCount: 69, transactionCount: 520 },
        { month: "Feb", totalSpend: 165000, avgSpend: 2200, customerCount: 75, transactionCount: 580 },
        { month: "Mar", totalSpend: 180000, avgSpend: 2300, customerCount: 78, transactionCount: 620 },
        { month: "Apr", totalSpend: 195000, avgSpend: 2450, customerCount: 80, transactionCount: 680 },
        { month: "May", totalSpend: 210000, avgSpend: 2600, customerCount: 81, transactionCount: 720 },
        { month: "Jun", totalSpend: 225000, avgSpend: 2750, customerCount: 82, transactionCount: 760 },
      ],
    };
  }

  // Create paginated response wrapper
  static createPaginatedResponse<T>(data: T[], page = 1, limit = 10): PaginatedResponse<T> {
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedData = data.slice(startIndex, endIndex);
    
    return {
      data: paginatedData,
      pagination: {
        page,
        limit,
        total: data.length,
        totalPages: Math.ceil(data.length / limit),
        hasNext: endIndex < data.length,
        hasPrev: page > 1,
      },
    };
  }
}
