# Backend Integration Guide

This document outlines the backend integration setup for the Customer Analytics Dashboard, specifically for the Merchant Admin role.

## Overview

The integration uses React Query for data fetching and state management, with a fallback system that provides mock data when the API is unavailable.

## Architecture

```
├── lib/
│   ├── api/
│   │   ├── client.ts              # Axios client configuration
│   │   ├── types.ts               # TypeScript interfaces
│   │   ├── merchant-admin.ts      # Merchant Admin API service
│   │   └── fallback-service.ts    # Mock data fallback
│   ├── hooks/
│   │   └── use-merchant-admin.ts  # React Query hooks
│   └── providers/
│       └── query-provider.tsx     # React Query provider
```

## Configuration

### Environment Variables

Create a `.env.local` file with the following variables:

```env
# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://*************:8000
NEXT_PUBLIC_USE_FALLBACK=true

# Development Settings
NEXT_PUBLIC_ENABLE_QUERY_DEVTOOLS=true
```

### API Client

The API client (`lib/api/client.ts`) provides:
- Centralized axios configuration
- Request/response interceptors
- Error handling
- Retry logic with exponential backoff

### Fallback System

When `NEXT_PUBLIC_USE_FALLBACK=true` or the API is unavailable, the system automatically falls back to mock data from `FallbackService`.

## Usage

### 1. Basic Data Fetching

```tsx
import { useHighValueCustomers } from '@/lib/hooks/use-merchant-admin';

function CustomerPage() {
  const { data, isLoading, error } = useHighValueCustomers({
    limit: 50,
    sortBy: 'totalSpend',
    sortOrder: 'desc'
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error loading data</div>;

  return <div>{/* Render customers */}</div>;
}
```

### 2. Dashboard Data

```tsx
import { useMerchantAdminDashboard } from '@/lib/hooks/use-merchant-admin';

function Dashboard() {
  const { data, isLoading, error } = useMerchantAdminDashboard({
    dateRange: {
      startDate: '2024-01-01',
      endDate: '2024-12-31'
    }
  });

  // Handle loading and error states
  // Render dashboard components
}
```

### 3. Customer Analytics

```tsx
import { useCustomerAnalytics } from '@/lib/hooks/use-merchant-admin';

function Analytics() {
  const { data, isLoading, error } = useCustomerAnalytics();
  
  const metrics = data?.metrics;
  const segmentAnalysis = data?.segmentAnalysis;
  const spendingTrends = data?.spendingTrends;
}
```

## Available Hooks

### Core Data Hooks
- `useMerchantAdminDashboard(params?)` - Complete dashboard data
- `useHighValueCustomers(params?)` - High-value customer list
- `useCustomerAnalytics(params?)` - Customer analytics and metrics
- `useBranchPerformance(params?)` - Branch performance data
- `useCustomerSegmentation(params?)` - Customer segmentation data
- `useSpendingTrends(params?)` - Spending trend analysis

### Individual Resource Hooks
- `useCustomer(customerId)` - Single customer details
- `useBranch(branchId)` - Single branch details
- `useBranchTellers(branchId)` - Tellers for a specific branch

### Utility Hooks
- `useInvalidateMerchantAdminQueries()` - Cache invalidation utilities

## API Endpoints

The system expects the following API endpoints:

```
GET /api/merchant-admin/dashboard
GET /api/merchant-admin/customers/high-value
GET /api/merchant-admin/customers/analytics
GET /api/merchant-admin/customers/{id}
GET /api/merchant-admin/branches/performance
GET /api/merchant-admin/branches/{id}
GET /api/merchant-admin/branches/{id}/tellers
GET /api/merchant-admin/customers/segmentation
GET /api/merchant-admin/customers/spending-trends
GET /api/merchant-admin/kpis
```

## Error Handling

The system includes comprehensive error handling:

1. **Network Errors**: Automatic retry with exponential backoff
2. **API Errors**: Graceful fallback to mock data
3. **Loading States**: Built-in loading indicators
4. **Error States**: User-friendly error messages

## Development

### React Query Devtools

In development mode, React Query Devtools are automatically enabled. Access them via the floating button in the bottom-right corner.

### Mock Data

The fallback service provides realistic mock data that matches the expected API response structure. This allows for development without a backend connection.

### Testing API Integration

1. Set `NEXT_PUBLIC_USE_FALLBACK=false` to test real API calls
2. Set `NEXT_PUBLIC_USE_FALLBACK=true` to use mock data
3. Monitor network requests in browser dev tools
4. Check React Query cache in the devtools

## Next Steps

1. **Authentication Integration**: Add auth headers to API requests
2. **Real-time Updates**: Implement WebSocket connections for live data
3. **Caching Strategy**: Fine-tune cache invalidation and refresh intervals
4. **Error Reporting**: Add error tracking and reporting
5. **Performance**: Implement data virtualization for large datasets

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure the API server allows requests from your domain
2. **Network Timeouts**: Adjust timeout settings in `lib/api/client.ts`
3. **Cache Issues**: Use React Query Devtools to inspect and clear cache
4. **Type Errors**: Ensure API responses match TypeScript interfaces

### Debug Mode

Enable debug logging by setting:
```env
NEXT_PUBLIC_DEBUG_API=true
```

This will log all API requests and responses to the console.
