"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  User,
  BarChart3,
  Users,
  AlertTriangle,
  Target,
  ArrowLeft
} from "lucide-react";

const tellerPages = [
  { href: "/teller/dashboard", title: "Dashboard", icon: User },
  { href: "/teller/high-value-customers", title: "High-Value Customers", icon: Target },
  { href: "/teller/personal-sales", title: "Sales Summary", icon: BarChart3 },
  { href: "/teller/interaction-tips", title: "Customer Tips", icon: Users },
  { href: "/teller/fraud-alerts", title: "Fraud Alerts", icon: AlertTriangle },
  { href: "/teller/performance-scorecard", title: "Scorecard", icon: Target },
];

export function TellerNav() {
  const pathname = usePathname();

  return (
    <Card className="mb-6">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/teller/dashboard">
              <Button variant="ghost" size="sm" className="flex items-center space-x-2">
                <ArrowLeft className="h-4 w-4" />
                <span>Teller Hub</span>
              </Button>
            </Link>
            <div className="h-4 w-px bg-border" />
            <div className="flex items-center space-x-2">
              {tellerPages.map((page) => {
                const Icon = page.icon;
                const isActive = pathname === page.href;

                return (
                  <Link key={page.href} href={page.href}>
                    <Button
                      variant={isActive ? "default" : "ghost"}
                      size="sm"
                      className="flex items-center space-x-2"
                    >
                      <Icon className="h-4 w-4" />
                      <span className="hidden md:inline">{page.title}</span>
                    </Button>
                  </Link>
                );
              })}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
