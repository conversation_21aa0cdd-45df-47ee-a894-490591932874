"use client";

import {
  <PERSON>,
  ArrowUpRight,
  ArrowDownRight,
  Users,
  DollarSign,
  TrendingUp,
  Target
} from "lucide-react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Legend
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatCurrency } from "@/lib/utils/currency";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

// High-Value Customer Data
const highValueCustomers = [
  { 
    id: "HVC001", 
    name: "<PERSON>", 
    phone: "+233 24 123 4567",
    totalSpend: 4250, 
    transactions: 18, 
    lastVisit: "2024-01-20", 
    avgValue: 236,
    segment: "Premium",
    tellerAssigned: "John Smith",
    spendTrend: 15.2
  },
  { 
    id: "HVC002", 
    name: "Emma Thompson", 
    phone: "+233 24 234 5678",
    totalSpend: 3890, 
    transactions: 15, 
    lastVisit: "2024-01-19", 
    avgValue: 259,
    segment: "High Value",
    tellerAssigned: "Sarah Johnson",
    spendTrend: 8.7
  },
  { 
    id: "HVC003", 
    name: "Robert Chen", 
    phone: "+233 24 345 6789",
    totalSpend: 3650, 
    transactions: 12, 
    lastVisit: "2024-01-18", 
    avgValue: 304,
    segment: "High Value",
    tellerAssigned: "Mike Brown",
    spendTrend: -2.1
  },
  { 
    id: "HVC004", 
    name: "Maria Garcia", 
    phone: "+233 24 456 7890",
    totalSpend: 3420, 
    transactions: 14, 
    lastVisit: "2024-01-17", 
    avgValue: 244,
    segment: "High Value",
    tellerAssigned: "Lisa Davis",
    spendTrend: 12.3
  },
  { 
    id: "HVC005", 
    name: "James Wilson", 
    phone: "+233 24 567 8901",
    totalSpend: 3200, 
    transactions: 11, 
    lastVisit: "2024-01-16", 
    avgValue: 291,
    segment: "High Value",
    tellerAssigned: "John Smith",
    spendTrend: 5.8
  },
];

// Revenue distribution data
const revenueDistributionData = [
  { segment: "Premium", customers: 1, revenue: 4250, percentage: 25.2 },
  { segment: "High Value", customers: 4, revenue: 12760, percentage: 74.8 },
  { segment: "Regular", customers: 45, revenue: 67500, percentage: 45.3 },
  { segment: "New", customers: 78, revenue: 23400, percentage: 15.7 },
];

// Time between transactions data
const timeBetweenTransactionsData = [
  { interval: "1-3 days", customers: 25, percentage: 18.5, avgSpend: 245 },
  { interval: "4-7 days", customers: 42, percentage: 31.1, avgSpend: 189 },
  { interval: "1-2 weeks", customers: 38, percentage: 28.1, avgSpend: 156 },
  { interval: "2-4 weeks", customers: 22, percentage: 16.3, avgSpend: 134 },
  { interval: "1+ months", customers: 8, percentage: 5.9, avgSpend: 98 },
];

export default function BranchAdminCustomersPage() {
  const { user } = useAuth();

  if (!user || user.role !== 'branch_admin') {
    return <div>Access denied. Branch Admin role required.</div>;
  }

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">High-Value Customer Analysis</h2>
        <p className="text-muted-foreground">
          Identify and analyze your most valuable customers for targeted retention strategies
        </p>
      </div>

      <FilterBar />

      {/* Customer KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="High-Value Customers"
          value="5"
          icon={Star}
          trend={{ value: 15.2, isPositive: true }}
        />
        <KpiCard
          title="Total HV Revenue"
          value={formatCurrency(18160)}
          icon={DollarSign}
          trend={{ value: 8.7, isPositive: true }}
        />
        <KpiCard
          title="Avg. Customer Value"
          value={formatCurrency(3632)}
          icon={Target}
          trend={{ value: 12.3, isPositive: true }}
        />
        <KpiCard
          title="Retention Rate"
          value="94.2%"
          icon={TrendingUp}
          trend={{ value: 5.8, isPositive: true }}
        />
      </div>

      {/* Charts Section */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Revenue Distribution */}
        <ChartContainer title="Revenue Distribution by Customer Segment">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={revenueDistributionData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="segment" />
              <YAxis />
              <Tooltip formatter={(value, name) => {
                if (name === "revenue") return [formatCurrency(Number(value)), "Revenue"];
                return [value, name];
              }} />
              <Legend />
              <Bar dataKey="revenue" fill={COLORS[0]} name="Revenue" />
              <Bar dataKey="customers" fill={COLORS[1]} name="Customer Count" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        {/* Customer Segment Pie Chart */}
        <ChartContainer title="Customer Segment Distribution">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={revenueDistributionData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ segment, percentage }) => `${segment}: ${percentage}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="percentage"
              >
                {revenueDistributionData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Time Between Transactions Analysis */}
      <ChartContainer title="Customer Return Frequency Analysis">
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={timeBetweenTransactionsData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="interval" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="customers" fill={COLORS[0]} name="Customer Count" />
            <Bar dataKey="avgSpend" fill={COLORS[1]} name="Avg Spend" />
          </BarChart>
        </ResponsiveContainer>
      </ChartContainer>

      {/* High-Value Customers Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Star className="h-5 w-5" />
            <span>High-Value Customer Details</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            title="Top High-Value Customers"
            columns={[
              { key: "name", title: "Customer Name" },
              { key: "phone", title: "Phone" },
              { key: "totalSpend", title: "Total Spend" },
              { key: "transactions", title: "Transactions" },
              { key: "avgValue", title: "Avg. Value" },
              { key: "segment", title: "Segment" },
              { key: "tellerAssigned", title: "Assigned Teller" },
              { 
                key: "spendTrend", 
                title: "Trend", 
                render: (value) => (
                  <div className="flex items-center space-x-1">
                    {value > 0 ? (
                      <ArrowUpRight className="h-4 w-4 text-green-500" />
                    ) : (
                      <ArrowDownRight className="h-4 w-4 text-red-500" />
                    )}
                    <span className={value > 0 ? "text-green-600" : "text-red-600"}>
                      {Math.abs(value)}%
                    </span>
                  </div>
                )
              },
            ]}
            data={highValueCustomers.map(customer => ({
              ...customer,
              totalSpend: formatCurrency(customer.totalSpend),
              avgValue: formatCurrency(customer.avgValue)
            }))}
          />
        </CardContent>
      </Card>
    </div>
  );
}
