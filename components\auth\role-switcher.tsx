"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { ChevronDown, Building2, Users, Store, User } from 'lucide-react';
import { useAuth } from '@/lib/auth/context';
import { getRoleDisplayName, getAvailableSwitchableEntities } from '@/lib/rbac/permissions';
import { UserRole } from '@/lib/auth/types';

const roleIcons: Record<UserRole, React.ComponentType<{ className?: string }>> = {
  agent: Building2,
  merchant: Store,
  merchant_admin: Users,
  branch_admin: Users,
  teller: User,
};

const roleColors: Record<UserRole, string> = {
  agent: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
  merchant: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  merchant_admin: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  branch_admin: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  teller: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
};

export function RoleSwitcher() {
  const { user, currentScope, switchScope } = useAuth();
  const [isOpen, setIsOpen] = useState(false);

  if (!user) return null;

  const availableEntities = getAvailableSwitchableEntities(user);
  const currentIcon = roleIcons[currentScope.level];
  const CurrentIcon = currentIcon;

  const handleSwitchScope = (entityId: string, entityName: string) => {
    switchScope(entityId, entityName);
    setIsOpen(false);
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="h-auto p-2 justify-start">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <CurrentIcon className="h-4 w-4" />
              <div className="text-left">
                <div className="text-sm font-medium">{currentScope.entityName}</div>
                <div className="text-xs text-muted-foreground">
                  {getRoleDisplayName(currentScope.level)}
                </div>
              </div>
            </div>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-80">
        <DropdownMenuLabel className="flex items-center space-x-2">
          <span>Switch View</span>
          <Badge className={roleColors[user.role]}>
            {getRoleDisplayName(user.role)}
          </Badge>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        <div className="p-2">
          <div className="text-xs text-muted-foreground mb-2">
            Current: {currentScope.entityName}
          </div>
          
          {availableEntities.length > 1 && (
            <div className="space-y-1">
              {availableEntities.map((entity) => {
                const EntityIcon = roleIcons[entity.type];
                const isCurrentEntity = entity.id === currentScope.entityId;
                
                return (
                  <DropdownMenuItem
                    key={entity.id}
                    onClick={() => handleSwitchScope(entity.id, entity.name)}
                    className={`flex items-center space-x-3 p-2 ${
                      isCurrentEntity ? 'bg-muted' : ''
                    }`}
                  >
                    <EntityIcon className="h-4 w-4" />
                    <div className="flex-1">
                      <div className="text-sm font-medium">{entity.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {getRoleDisplayName(entity.type)}
                      </div>
                    </div>
                    {isCurrentEntity && (
                      <Badge variant="secondary" className="text-xs">
                        Current
                      </Badge>
                    )}
                  </DropdownMenuItem>
                );
              })}
            </div>
          )}
          
          {availableEntities.length === 1 && (
            <div className="text-xs text-muted-foreground p-2">
              No other entities available for your role level.
            </div>
          )}
        </div>
        
        <DropdownMenuSeparator />
        <div className="p-2 text-xs text-muted-foreground">
          <div className="flex items-center justify-between">
            <span>Data Scope:</span>
            <Badge variant="outline" className="text-xs">
              {currentScope.canViewChildren ? 'Hierarchical' : 'Own Only'}
            </Badge>
          </div>
          <div className="mt-1">
            Viewing {currentScope.accessibleEntityIds.length} entities
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
