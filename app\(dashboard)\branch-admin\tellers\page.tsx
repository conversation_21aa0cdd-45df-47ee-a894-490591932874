"use client";

import {
  Award,
  Users,
  Timer,
  Star,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  TrendingUp,
  Activity,
  Target
} from "lucide-react";
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ScatterChart,
  Scatter,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  LineChart,
  Line
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatCurrency } from "@/lib/utils/currency";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

// Teller Performance Data
const tellerPerformanceData = [
  { 
    name: "<PERSON>", 
    id: "T001",
    transactions: 189, 
    avgValue: 278, 
    score: 94, 
    efficiency: 92,
    customersSaved: 45,
    avgProcessingTime: 2.1,
    errorRate: 0.8,
    customerRating: 4.8
  },
  { 
    name: "Sarah Johnson", 
    id: "T002",
    transactions: 176, 
    avgValue: 312, 
    score: 91, 
    efficiency: 88,
    customersSaved: 38,
    avgProcessingTime: 2.3,
    errorRate: 1.2,
    customerRating: 4.6
  },
  { 
    name: "Mike Brown", 
    id: "T003",
    transactions: 162, 
    avgValue: 258, 
    score: 88, 
    efficiency: 85,
    customersSaved: 41,
    avgProcessingTime: 2.5,
    errorRate: 1.5,
    customerRating: 4.4
  },
  { 
    name: "Lisa Davis", 
    id: "T004",
    transactions: 195, 
    avgValue: 273, 
    score: 96, 
    efficiency: 95,
    customersSaved: 52,
    avgProcessingTime: 1.9,
    errorRate: 0.5,
    customerRating: 4.9
  },
  { 
    name: "Tom Wilson", 
    id: "T005",
    transactions: 151, 
    avgValue: 245, 
    score: 82, 
    efficiency: 80,
    customersSaved: 29,
    avgProcessingTime: 2.8,
    errorRate: 2.1,
    customerRating: 4.2
  },
];

// Radar chart data for teller skills
const tellerSkillsData = tellerPerformanceData.map(teller => ({
  name: teller.name,
  efficiency: teller.efficiency,
  customerService: teller.customerRating * 20, // Convert to 100 scale
  accuracy: 100 - (teller.errorRate * 10), // Convert error rate to accuracy
  speed: 100 - (teller.avgProcessingTime * 10), // Convert processing time to speed score
  sales: (teller.avgValue / 312) * 100, // Normalize to highest performer
}));

// Performance trends over time
const performanceTrends = [
  { month: "Sep", johnSmith: 89, sarahJohnson: 87, mikeBrown: 85, lisaDavis: 92, tomWilson: 78 },
  { month: "Oct", johnSmith: 91, sarahJohnson: 89, mikeBrown: 86, lisaDavis: 94, tomWilson: 79 },
  { month: "Nov", johnSmith: 92, sarahJohnson: 90, mikeBrown: 87, lisaDavis: 95, tomWilson: 80 },
  { month: "Dec", johnSmith: 93, sarahJohnson: 90, mikeBrown: 87, lisaDavis: 95, tomWilson: 81 },
  { month: "Jan", johnSmith: 94, sarahJohnson: 91, mikeBrown: 88, lisaDavis: 96, tomWilson: 82 },
];

export default function BranchAdminTellersPage() {
  const { user } = useAuth();

  if (!user || user.role !== 'branch_admin') {
    return <div>Access denied. Branch Admin role required.</div>;
  }

  const topPerformer = tellerPerformanceData.reduce((prev, current) => 
    (prev.score > current.score) ? prev : current
  );

  const avgProcessingTime = tellerPerformanceData.reduce((sum, teller) => 
    sum + teller.avgProcessingTime, 0) / tellerPerformanceData.length;

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Teller Performance Management</h2>
        <p className="text-muted-foreground">
          Comprehensive oversight and analysis of teller performance metrics
        </p>
      </div>

      <FilterBar />

      {/* Teller KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Active Tellers"
          value="5"
          icon={Users}
          trend={{ value: 0, isPositive: true }}
        />
        <KpiCard
          title="Avg. Performance Score"
          value="90.2"
          icon={Award}
          trend={{ value: 3.2, isPositive: true }}
        />
        <KpiCard
          title="Avg. Processing Time"
          value={`${avgProcessingTime.toFixed(1)} min`}
          icon={Timer}
          trend={{ value: 8.5, isPositive: false }}
        />
        <KpiCard
          title="Customer Satisfaction"
          value="4.6/5"
          icon={Star}
          trend={{ value: 2.1, isPositive: true }}
        />
      </div>

      {/* Performance Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Teller Performance Scorecard */}
        <ChartContainer title="Teller Performance Scorecard">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={tellerPerformanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
              <YAxis />
              <Tooltip />
              <Bar dataKey="score" fill={COLORS[0]} name="Performance Score" />
              <Bar dataKey="efficiency" fill={COLORS[1]} name="Efficiency %" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        {/* Efficiency vs Customer Rating */}
        <ChartContainer title="Efficiency vs Customer Rating Correlation">
          <ResponsiveContainer width="100%" height="100%">
            <ScatterChart data={tellerPerformanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="efficiency" name="Efficiency %" />
              <YAxis dataKey="customerRating" name="Customer Rating" />
              <Tooltip />
              <Scatter dataKey="customerRating" fill={COLORS[0]} />
            </ScatterChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Skills Radar Chart */}
      <ChartContainer title="Teller Skills Assessment">
        <ResponsiveContainer width="100%" height={400}>
          <RadarChart data={tellerSkillsData}>
            <PolarGrid />
            <PolarAngleAxis dataKey="name" />
            <PolarRadiusAxis angle={90} domain={[0, 100]} />
            <Radar name="Efficiency" dataKey="efficiency" stroke={COLORS[0]} fill={COLORS[0]} fillOpacity={0.1} />
            <Radar name="Customer Service" dataKey="customerService" stroke={COLORS[1]} fill={COLORS[1]} fillOpacity={0.1} />
            <Radar name="Accuracy" dataKey="accuracy" stroke={COLORS[2]} fill={COLORS[2]} fillOpacity={0.1} />
            <Radar name="Speed" dataKey="speed" stroke={COLORS[3]} fill={COLORS[3]} fillOpacity={0.1} />
            <Tooltip />
          </RadarChart>
        </ResponsiveContainer>
      </ChartContainer>

      {/* Performance Trends */}
      <ChartContainer title="Performance Trends Over Time">
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={performanceTrends}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Line type="monotone" dataKey="johnSmith" stroke={COLORS[0]} name="John Smith" />
            <Line type="monotone" dataKey="sarahJohnson" stroke={COLORS[1]} name="Sarah Johnson" />
            <Line type="monotone" dataKey="mikeBrown" stroke={COLORS[2]} name="Mike Brown" />
            <Line type="monotone" dataKey="lisaDavis" stroke={COLORS[3]} name="Lisa Davis" />
            <Line type="monotone" dataKey="tomWilson" stroke={COLORS[4]} name="Tom Wilson" />
          </LineChart>
        </ResponsiveContainer>
      </ChartContainer>

      {/* Detailed Performance Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Award className="h-5 w-5" />
            <span>Detailed Teller Performance Metrics</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            title="Teller Performance Details"
            columns={[
              { key: "name", title: "Teller Name" },
              { key: "id", title: "ID" },
              { key: "transactions", title: "Transactions" },
              { key: "avgValue", title: "Avg. Value" },
              { key: "score", title: "Score" },
              { key: "efficiency", title: "Efficiency %" },
              { key: "avgProcessingTime", title: "Avg. Time (min)" },
              { key: "errorRate", title: "Error Rate %" },
              { key: "customerRating", title: "Rating" },
              { 
                key: "customersSaved", 
                title: "Customers Served", 
                render: (value) => (
                  <Badge variant="outline">{value}</Badge>
                )
              },
            ]}
            data={tellerPerformanceData.map(teller => ({
              ...teller,
              avgValue: formatCurrency(teller.avgValue)
            }))}
          />
        </CardContent>
      </Card>

      {/* Performance Insights */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Top Performer</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{topPerformer.name}</div>
            <p className="text-xs text-muted-foreground">
              {topPerformer.score}% performance score
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Fastest Processing</CardTitle>
            <Timer className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1.9 min</div>
            <p className="text-xs text-muted-foreground">
              Lisa Davis average
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Highest Rating</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4.9/5</div>
            <p className="text-xs text-muted-foreground">
              Customer satisfaction
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Needs Attention</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Tom Wilson</div>
            <p className="text-xs text-muted-foreground">
              Below average performance
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
