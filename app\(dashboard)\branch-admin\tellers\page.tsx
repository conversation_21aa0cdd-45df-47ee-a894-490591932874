"use client";

import {
  Award,
  Users,
  Timer,
  Star,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  TrendingUp,
  Activity,
  Target
} from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatCurrency } from "@/lib/utils/currency";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

// Teller Performance Data (removed efficiency metrics)
const tellerPerformanceData = [
  {
    name: "<PERSON>",
    id: "T001",
    transactions: 189,
    avgValue: 278,
    score: 94,
    customersSaved: 45
  },
  {
    name: "<PERSON>",
    id: "T002",
    transactions: 176,
    avgValue: 312,
    score: 91,
    customersSaved: 38
  },
  {
    name: "Mike Brown",
    id: "T003",
    transactions: 162,
    avgValue: 258,
    score: 88,
    customersSaved: 41
  },
  {
    name: "Lisa Davis",
    id: "T004",
    transactions: 195,
    avgValue: 273,
    score: 96,
    customersSaved: 52
  },
  {
    name: "Tom Wilson",
    id: "T005",
    transactions: 151,
    avgValue: 245,
    score: 82,
    customersSaved: 29
  },
];

// Transaction volume data for teller comparison
const tellerTransactionData = tellerPerformanceData.map(teller => ({
  name: teller.name,
  transactions: teller.transactions,
  avgValue: teller.avgValue,
  score: teller.score,
  customersSaved: teller.customersSaved
}));

export default function BranchAdminTellersPage() {
  const { user } = useAuth();

  if (!user || user.role !== 'branch_admin') {
    return <div>Access denied. Branch Admin role required.</div>;
  }

  const topPerformer = tellerPerformanceData.reduce((prev, current) =>
    (prev.score > current.score) ? prev : current
  );

  const avgScore = tellerPerformanceData.reduce((sum, teller) =>
    sum + teller.score, 0) / tellerPerformanceData.length;

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Teller Performance Management</h2>
        <p className="text-muted-foreground">
          Comprehensive oversight and analysis of teller performance metrics
        </p>
      </div>

      <FilterBar />

      {/* Teller KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Active Tellers"
          value="5"
          icon={Users}
          trend={{ value: 0, isPositive: true }}
        />
        <KpiCard
          title="Avg. Performance Score"
          value={avgScore.toFixed(1)}
          icon={Award}
          trend={{ value: 3.2, isPositive: true }}
        />
        <KpiCard
          title="Total Transactions"
          value={tellerPerformanceData.reduce((sum, t) => sum + t.transactions, 0).toString()}
          icon={Timer}
          trend={{ value: 8.5, isPositive: true }}
        />
        <KpiCard
          title="Customers Served"
          value={tellerPerformanceData.reduce((sum, t) => sum + t.customersSaved, 0).toString()}
          icon={Star}
          trend={{ value: 2.1, isPositive: true }}
        />
      </div>

      {/* Performance Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Teller Performance Scorecard */}
        <ChartContainer title="Teller Performance Scorecard">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={tellerPerformanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
              <YAxis />
              <Tooltip />
              <Bar dataKey="score" fill={COLORS[0]} name="Performance Score" />
              <Bar dataKey="transactions" fill={COLORS[1]} name="Transactions" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        {/* Transaction Value Analysis */}
        <ChartContainer title="Transaction Value Analysis">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={tellerPerformanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
              <YAxis />
              <Tooltip formatter={(value, name) => {
                if (name === "avgValue") return [formatCurrency(Number(value)), "Avg Value"];
                return [value, name];
              }} />
              <Bar dataKey="avgValue" fill={COLORS[2]} name="Avg Value" />
              <Bar dataKey="customersSaved" fill={COLORS[3]} name="Customers Served" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Teller Performance Overview */}
      <ChartContainer title="Teller Performance Overview">
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={tellerPerformanceData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip formatter={(value, name) => {
              if (name === "avgValue") return [formatCurrency(Number(value)), "Avg Value"];
              return [value, name];
            }} />
            <Legend />
            <Bar dataKey="transactions" fill={COLORS[0]} name="Transactions" />
            <Bar dataKey="score" fill={COLORS[1]} name="Performance Score" />
          </BarChart>
        </ResponsiveContainer>
      </ChartContainer>

      {/* Detailed Performance Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Award className="h-5 w-5" />
            <span>Detailed Teller Performance Metrics</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            title="Teller Performance Details"
            columns={[
              { key: "name", title: "Teller Name" },
              { key: "id", title: "ID" },
              { key: "transactions", title: "Transactions" },
              { key: "avgValue", title: "Avg. Value" },
              { key: "score", title: "Score" },
              {
                key: "customersSaved",
                title: "Customers Served",
                render: (value) => (
                  <Badge variant="outline">{value}</Badge>
                )
              },
            ]}
            data={tellerPerformanceData.map(teller => ({
              ...teller,
              avgValue: formatCurrency(teller.avgValue)
            }))}
          />
        </CardContent>
      </Card>

      {/* Performance Insights */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Top Performer</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{topPerformer.name}</div>
            <p className="text-xs text-muted-foreground">
              {topPerformer.score}% performance score
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Most Transactions</CardTitle>
            <Timer className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">195</div>
            <p className="text-xs text-muted-foreground">
              Lisa Davis total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Highest Avg Value</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(312)}</div>
            <p className="text-xs text-muted-foreground">
              Sarah Johnson average
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Needs Attention</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Tom Wilson</div>
            <p className="text-xs text-muted-foreground">
              Below average performance
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
