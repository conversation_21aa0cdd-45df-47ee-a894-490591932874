"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth/context';

export default function HomePage() {
  const { isAuthenticated, isLoading, user } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      if (isAuthenticated && user) {
        // Redirect to role-specific dashboard
        const dashboardRoutes = {
            agent: '/agent/dashboard',
            merchant: '/', // General dashboard at root
            merchant_admin: '/merchant-admin/dashboard',
            branch_admin: '/branch-admin/dashboard',
            teller: '/teller/dashboard',
        };
        const redirectTo = dashboardRoutes[user.role];

        // Only redirect if it's not the current page
        if (redirectTo && redirectTo !== '/') {
          router.push(redirectTo);
        }
      } else {
        router.push('/login');
      }
    }
  }, [isAuthenticated, isLoading, user, router]);

  return (
    <div className="flex h-screen items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-muted-foreground">Loading...</p>
      </div>
    </div>
  );
}
