// Mock customer data
export const customers = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    totalSpend: 2450.75,
    lastTransaction: "2023-05-15",
    transactionCount: 12,
    avgTransactionValue: 204.23,
    customerSince: "2022-01-10",
    segment: "High Value",
    status: "Active",
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    totalSpend: 1875.50,
    lastTransaction: "2023-05-18",
    transactionCount: 8,
    avgTransactionValue: 234.44,
    customerSince: "2022-03-22",
    segment: "Regular",
    status: "Active",
  },
  {
    id: 3,
    name: "<PERSON>",
    email: "<EMAIL>",
    totalSpend: 3200.25,
    lastTransaction: "2023-05-10",
    transactionCount: 15,
    avgTransactionValue: 213.35,
    customerSince: "2021-11-05",
    segment: "High Value",
    status: "Active",
  },
  {
    id: 4,
    name: "<PERSON>",
    email: "<EMAIL>",
    totalSpend: 950.30,
    lastTransaction: "2023-04-28",
    transactionCount: 5,
    avgTransactionValue: 190.06,
    customerSince: "2022-06-15",
    segment: "New",
    status: "At Risk",
  },
  {
    id: 5,
    name: "David Wilson",
    email: "<EMAIL>",
    totalSpend: 4150.90,
    lastTransaction: "2023-05-20",
    transactionCount: 18,
    avgTransactionValue: 230.61,
    customerSince: "2021-08-12",
    segment: "High Value",
    status: "Active",
  },
];

// Mock transaction volume data
export const transactionVolumeData = [
  { month: "Jan", volume: 120 },
  { month: "Feb", volume: 150 },
  { month: "Mar", volume: 180 },
  { month: "Apr", volume: 220 },
  { month: "May", volume: 250 },
  { month: "Jun", volume: 280 },
  { month: "Jul", volume: 310 },
  { month: "Aug", volume: 290 },
  { month: "Sep", volume: 320 },
  { month: "Oct", volume: 350 },
  { month: "Nov", volume: 380 },
  { month: "Dec", volume: 410 },
];

// Mock average transaction value data
export const avgTransactionValueData = [
  { month: "Jan", value: 185 },
  { month: "Feb", value: 190 },
  { month: "Mar", value: 195 },
  { month: "Apr", value: 205 },
  { month: "May", value: 210 },
  { month: "Jun", value: 215 },
  { month: "Jul", value: 220 },
  { month: "Aug", value: 225 },
  { month: "Sep", value: 230 },
  { month: "Oct", value: 235 },
  { month: "Nov", value: 240 },
  { month: "Dec", value: 245 },
];

// Mock customer segmentation data
export const customerSegmentationData = [
  { name: "High Value", value: 30 },
  { name: "Regular", value: 45 },
  { name: "Occasional", value: 15 },
  { name: "New", value: 10 },
];

// Mock spend patterns data (day vs time heatmap)
export const spendPatternsData = [
  { day: "Monday", hour: "Morning", value: 120 },
  { day: "Monday", hour: "Afternoon", value: 150 },
  { day: "Monday", hour: "Evening", value: 180 },
  { day: "Tuesday", hour: "Morning", value: 130 },
  { day: "Tuesday", hour: "Afternoon", value: 160 },
  { day: "Tuesday", hour: "Evening", value: 190 },
  { day: "Wednesday", hour: "Morning", value: 140 },
  { day: "Wednesday", hour: "Afternoon", value: 170 },
  { day: "Wednesday", hour: "Evening", value: 200 },
  { day: "Thursday", hour: "Morning", value: 150 },
  { day: "Thursday", hour: "Afternoon", value: 180 },
  { day: "Thursday", hour: "Evening", value: 210 },
  { day: "Friday", hour: "Morning", value: 160 },
  { day: "Friday", hour: "Afternoon", value: 190 },
  { day: "Friday", hour: "Evening", value: 220 },
  { day: "Saturday", hour: "Morning", value: 170 },
  { day: "Saturday", hour: "Afternoon", value: 200 },
  { day: "Saturday", hour: "Evening", value: 230 },
  { day: "Sunday", hour: "Morning", value: 180 },
  { day: "Sunday", hour: "Afternoon", value: 210 },
  { day: "Sunday", hour: "Evening", value: 240 },
];

// Mock time between transactions data
export const timeBetweenTransactionsData = [
  { segment: "High Value", days: 7 },
  { segment: "Regular", days: 14 },
  { segment: "Occasional", days: 30 },
  { segment: "New", days: 21 },
];

// Mock returning customers data
export const returningCustomersData = [
  { month: "Jan", count: 45 },
  { month: "Feb", count: 50 },
  { month: "Mar", count: 55 },
  { month: "Apr", count: 60 },
  { month: "May", count: 65 },
  { month: "Jun", count: 70 },
  { month: "Jul", count: 75 },
  { month: "Aug", count: 80 },
  { month: "Sep", count: 85 },
  { month: "Oct", count: 90 },
  { month: "Nov", count: 95 },
  { month: "Dec", count: 100 },
];

// Mock growth forecast data
export const growthForecastData = [
  { month: "Jan", actual: 100, forecast: null },
  { month: "Feb", actual: 120, forecast: null },
  { month: "Mar", actual: 140, forecast: null },
  { month: "Apr", actual: 160, forecast: null },
  { month: "May", actual: 180, forecast: null },
  { month: "Jun", actual: 200, forecast: null },
  { month: "Jul", actual: 220, forecast: null },
  { month: "Aug", actual: 240, forecast: null },
  { month: "Sep", actual: 260, forecast: null },
  { month: "Oct", actual: 280, forecast: null },
  { month: "Nov", actual: 300, forecast: null },
  { month: "Dec", actual: 320, forecast: null },
  { month: "Jan (Next)", actual: null, forecast: 340 },
  { month: "Feb (Next)", actual: null, forecast: 360 },
  { month: "Mar (Next)", actual: null, forecast: 380 },
];

// Mock alerts data
export const alertsData = [
  {
    id: 1,
    type: "Unusual Activity",
    customer: "John Smith",
    description: "Sudden increase in transaction frequency",
    date: "2023-05-20",
    severity: "medium",
  },
  {
    id: 2,
    type: "High Value Transaction",
    customer: "David Wilson",
    description: "Transaction value exceeds 3x average",
    date: "2023-05-19",
    severity: "high",
  },
  {
    id: 3,
    type: "Churn Risk",
    customer: "Emily Davis",
    description: "No activity in last 30 days",
    date: "2023-05-18",
    severity: "medium",
  },
  {
    id: 4,
    type: "Fraud Suspicion",
    customer: "Anonymous User",
    description: "Multiple failed payment attempts",
    date: "2023-05-17",
    severity: "high",
  },
  {
    id: 5,
    type: "New High Value Customer",
    customer: "Sarah Johnson",
    description: "Customer reached high value segment",
    date: "2023-05-16",
    severity: "low",
  },
];
