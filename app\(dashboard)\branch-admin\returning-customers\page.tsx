"use client";

import { useState } from "react";
import {
  Users,
  UserPlus,
  Repeat,
  TrendingUp,
  DollarSign,
  Target,
  UserCheck,
  Activity
} from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend,
  LineChart,
  Line,
  AreaChart,
  Area
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { formatCurrency } from "@/lib/utils/currency";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

// Filter periods
type FilterPeriod = "7days" | "30days" | "90days" | "custom";

// Mock data generator for returning customers
const generateReturningCustomerData = (period: FilterPeriod) => {
  const baseMultiplier = period === "7days" ? 0.3 : period === "30days" ? 1 : period === "90days" ? 2.5 : 1;

  return {
    // KPI Data
    kpis: {
      totalCustomers: Math.round(566 * baseMultiplier),
      newCustomers: Math.round(89 * baseMultiplier),
      returningCustomers: Math.round(477 * baseMultiplier),
      retentionRate: 84.3,
      avgReturnTime: 12, // days
    },

    // New vs Returning Customer Analysis
    newVsReturning: [
      {
        type: "New Customers",
        count: Math.round(89 * baseMultiplier),
        revenue: Math.round(134000 * baseMultiplier),
        avgTransactionValue: 185,
        percentage: 15.7,
        transactions: Math.round(724 * baseMultiplier),
      },
      {
        type: "Returning Customers",
        count: Math.round(477 * baseMultiplier),
        revenue: Math.round(689000 * baseMultiplier),
        avgTransactionValue: 245,
        percentage: 84.3,
        transactions: Math.round(2812 * baseMultiplier),
      },
    ],

    // Customer Retention Trends
    retentionTrends: [
      { period: period === "7days" ? "Day 1" : period === "30days" ? "Week 1" : "Month 1",
        newCustomers: Math.round(12 * baseMultiplier),
        returningCustomers: Math.round(78 * baseMultiplier),
        retentionRate: 86.7 },
      { period: period === "7days" ? "Day 2" : period === "30days" ? "Week 2" : "Month 2",
        newCustomers: Math.round(18 * baseMultiplier),
        returningCustomers: Math.round(89 * baseMultiplier),
        retentionRate: 83.2 },
      { period: period === "7days" ? "Day 3" : period === "30days" ? "Week 3" : "Month 3",
        newCustomers: Math.round(15 * baseMultiplier),
        returningCustomers: Math.round(95 * baseMultiplier),
        retentionRate: 86.4 },
      { period: period === "7days" ? "Day 4" : period === "30days" ? "Week 4" : "Month 4",
        newCustomers: Math.round(22 * baseMultiplier),
        returningCustomers: Math.round(102 * baseMultiplier),
        retentionRate: 82.3 },
      { period: period === "7days" ? "Day 5" : period === "30days" ? "Current" : "Month 5",
        newCustomers: Math.round(19 * baseMultiplier),
        returningCustomers: Math.round(98 * baseMultiplier),
        retentionRate: 83.8 },
    ],

    // Top Repeat Customers
    topRepeatCustomers: [
      {
        name: "David Wilson",
        phone: "+233 24 123 4567",
        totalTransactions: Math.round(45 * baseMultiplier),
        totalSpend: Math.round(12500 * baseMultiplier),
        avgTransactionValue: 278,
        firstVisit: "2023-08-15",
        lastVisit: "2024-01-20",
        frequency: "Every 2 days",
      },
      {
        name: "Emma Thompson",
        phone: "+233 24 234 5678",
        totalTransactions: Math.round(38 * baseMultiplier),
        totalSpend: Math.round(9800 * baseMultiplier),
        avgTransactionValue: 258,
        firstVisit: "2023-09-22",
        lastVisit: "2024-01-19",
        frequency: "Every 3 days",
      },
      {
        name: "Robert Chen",
        phone: "+233 24 345 6789",
        totalTransactions: Math.round(32 * baseMultiplier),
        totalSpend: Math.round(8900 * baseMultiplier),
        avgTransactionValue: 278,
        firstVisit: "2023-07-10",
        lastVisit: "2024-01-18",
        frequency: "Weekly",
      },
      {
        name: "Maria Garcia",
        phone: "+233 24 456 7890",
        totalTransactions: Math.round(28 * baseMultiplier),
        totalSpend: Math.round(7200 * baseMultiplier),
        avgTransactionValue: 257,
        firstVisit: "2023-10-05",
        lastVisit: "2024-01-17",
        frequency: "Bi-weekly",
      },
      {
        name: "James Wilson",
        phone: "+233 24 567 8901",
        totalTransactions: Math.round(25 * baseMultiplier),
        totalSpend: Math.round(6800 * baseMultiplier),
        avgTransactionValue: 272,
        firstVisit: "2023-11-12",
        lastVisit: "2024-01-16",
        frequency: "Weekly",
      },
    ],

    // Customer Return Patterns
    returnPatterns: [
      { pattern: "Daily (1-2 days)", customers: Math.round(45 * baseMultiplier), percentage: 9.4, avgSpend: 4200 },
      { pattern: "Weekly (3-7 days)", customers: Math.round(156 * baseMultiplier), percentage: 32.7, avgSpend: 2800 },
      { pattern: "Bi-weekly (8-14 days)", customers: Math.round(134 * baseMultiplier), percentage: 28.1, avgSpend: 1900 },
      { pattern: "Monthly (15-30 days)", customers: Math.round(98 * baseMultiplier), percentage: 20.5, avgSpend: 1200 },
      { pattern: "Quarterly (30+ days)", customers: Math.round(44 * baseMultiplier), percentage: 9.2, avgSpend: 800 },
    ],

    // Customer Growth Analysis
    customerGrowth: [
      { period: period === "7days" ? "Day 1" : period === "30days" ? "Week 1" : "Month 1",
        newAcquired: Math.round(12 * baseMultiplier),
        retained: Math.round(78 * baseMultiplier),
        churned: Math.round(8 * baseMultiplier) },
      { period: period === "7days" ? "Day 2" : period === "30days" ? "Week 2" : "Month 2",
        newAcquired: Math.round(18 * baseMultiplier),
        retained: Math.round(89 * baseMultiplier),
        churned: Math.round(12 * baseMultiplier) },
      { period: period === "7days" ? "Day 3" : period === "30days" ? "Week 3" : "Month 3",
        newAcquired: Math.round(15 * baseMultiplier),
        retained: Math.round(95 * baseMultiplier),
        churned: Math.round(9 * baseMultiplier) },
      { period: period === "7days" ? "Day 4" : period === "30days" ? "Week 4" : "Month 4",
        newAcquired: Math.round(22 * baseMultiplier),
        retained: Math.round(102 * baseMultiplier),
        churned: Math.round(15 * baseMultiplier) },
      { period: period === "7days" ? "Day 5" : period === "30days" ? "Current" : "Month 5",
        newAcquired: Math.round(19 * baseMultiplier),
        retained: Math.round(98 * baseMultiplier),
        churned: Math.round(11 * baseMultiplier) },
    ],
  };
};

export default function ReturningCustomersPage() {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<FilterPeriod>("30days");

  if (!user || user.role !== 'branch_admin') {
    return <div>Access denied. Branch Admin role required.</div>;
  }

  // Generate data based on selected filter period
  const data = generateReturningCustomerData(selectedPeriod);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "VIP":
        return <Badge className="bg-purple-600">{status}</Badge>;
      case "Gold":
        return <Badge className="bg-yellow-600">{status}</Badge>;
      case "Silver":
        return <Badge className="bg-gray-600">{status}</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="w-full max-w-full overflow-hidden space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Returning Customer Analysis</h2>
        <p className="text-muted-foreground">
          Track customer loyalty, retention patterns, and repeat visit behaviors
        </p>
      </div>

      <FilterBar />

      {/* Returning Customer KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-5">
        <KpiCard
          title="Total Customers"
          value={data.kpis.totalCustomers.toString()}
          icon={Users}
          trend={{ value: 8.2, isPositive: true }}
        />
        <KpiCard
          title="New Customers"
          value={data.kpis.newCustomers.toString()}
          icon={UserPlus}
          trend={{ value: 12.5, isPositive: true }}
        />
        <KpiCard
          title="Returning Customers"
          value={data.kpis.returningCustomers.toString()}
          icon={Repeat}
          trend={{ value: 6.8, isPositive: true }}
        />
        <KpiCard
          title="Retention Rate"
          value={`${data.kpis.retentionRate}%`}
          icon={UserCheck}
          trend={{ value: 3.2, isPositive: true }}
        />
        <KpiCard
          title="Avg. Return Time"
          value={`${data.kpis.avgReturnTime} days`}
          icon={Target}
          trend={{ value: 8.5, isPositive: false }}
        />
      </div>

      {/* Customer Analysis Charts */}
      <div className="grid gap-6 md:grid-cols-2 max-w-full">
        {/* New vs Returning Distribution */}
        <ChartContainer title="New vs Returning Customer Distribution">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data.newVsReturning}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ type, percentage }) => `${type}: ${percentage}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="percentage"
              >
                {data.newVsReturning.map((_, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>

        {/* Revenue Comparison */}
        <ChartContainer title="Revenue: New vs Returning Customers">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data.newVsReturning}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="type" />
              <YAxis />
              <Tooltip formatter={(value, name) => {
                if (name === "revenue") return [formatCurrency(Number(value)), "Revenue"];
                if (name === "avgTransactionValue") return [formatCurrency(Number(value)), "Avg Transaction"];
                return [value, name];
              }} />
              <Legend />
              <Bar dataKey="revenue" fill={COLORS[0]} name="Revenue" />
              <Bar dataKey="avgTransactionValue" fill={COLORS[1]} name="Avg Transaction" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Customer Return Patterns */}
      <ChartContainer title="Customer Return Frequency Patterns">
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data.returnPatterns}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="pattern" />
            <YAxis />
            <Tooltip formatter={(value, name) => {
              if (name === "avgSpend") return [formatCurrency(Number(value)), "Avg Spend"];
              return [value, name];
            }} />
            <Legend />
            <Bar dataKey="customers" fill={COLORS[0]} name="Customer Count" />
            <Bar dataKey="avgSpend" fill={COLORS[1]} name="Avg Spend" />
          </BarChart>
        </ResponsiveContainer>
      </ChartContainer>

      {/* Customer Growth Trends */}
      <ChartContainer title="Customer Acquisition & Retention Trends">
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={data.customerGrowth}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="period" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Area type="monotone" dataKey="newAcquired" stackId="1" stroke={COLORS[1]} fill={COLORS[1]} name="New Acquired" />
            <Area type="monotone" dataKey="retained" stackId="1" stroke={COLORS[0]} fill={COLORS[0]} name="Retained" />
            <Area type="monotone" dataKey="churned" stackId="2" stroke={COLORS[3]} fill={COLORS[3]} name="Churned" />
          </AreaChart>
        </ResponsiveContainer>
      </ChartContainer>

      {/* Top Repeat Customers Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Repeat className="h-5 w-5" />
            <span>Top Repeat Customers</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            title="Most Loyal Customers"
            columns={[
              { key: "name", title: "Customer Name" },
              { key: "phone", title: "Phone Number" },
              { key: "totalTransactions", title: "Total Transactions" },
              { key: "totalSpend", title: "Total Spend" },
              { key: "avgTransactionValue", title: "Avg. Transaction" },
              { key: "frequency", title: "Visit Frequency" },
              { key: "firstVisit", title: "First Visit" },
              { key: "lastVisit", title: "Last Visit" },
            ]}
            data={data.topRepeatCustomers.map(customer => ({
              ...customer,
              totalSpend: formatCurrency(customer.totalSpend),
              avgTransactionValue: formatCurrency(customer.avgTransactionValue)
            }))}
          />
        </CardContent>
      </Card>
    </div>
  );
}
