"use client";

import { useState } from "react";
import {
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Users,
  TrendingUp,
  DollarSign,
  Target,
  Star,
  UserCheck,
  Activity
} from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { formatCurrency } from "@/lib/utils/currency";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

// Filter periods
type FilterPeriod = "7days" | "30days" | "90days" | "custom";

// Mock data generator for customer segmentation
const generateSegmentationData = (period: FilterPeriod) => {
  const baseMultiplier = period === "7days" ? 0.3 : period === "30days" ? 1 : period === "90days" ? 2.5 : 1;
  
  return {
    // KPI Data
    kpis: {
      totalCustomers: Math.round(566 * baseMultiplier),
      activeSegments: 5,
      highValueCustomers: Math.round(89 * baseMultiplier),
      avgSegmentValue: 2450,
    },

    // Customer Segments by Spending Level
    spendingSegments: [
      {
        segment: "Premium",
        description: "₵5000+ per month",
        count: Math.round(25 * baseMultiplier),
        percentage: 4.4,
        avgSpend: 7500,
        avgTransactionValue: 450,
        retentionRate: 95,
        frequency: "Weekly"
      },
      {
        segment: "High Value",
        description: "₵2000-₵4999 per month",
        count: Math.round(64 * baseMultiplier),
        percentage: 11.3,
        avgSpend: 3200,
        avgTransactionValue: 320,
        retentionRate: 88,
        frequency: "Bi-weekly"
      },
      {
        segment: "Regular",
        description: "₵500-₵1999 per month",
        count: Math.round(203 * baseMultiplier),
        percentage: 35.9,
        avgSpend: 1200,
        avgTransactionValue: 180,
        retentionRate: 72,
        frequency: "Monthly"
      },
      {
        segment: "Occasional",
        description: "₵100-₵499 per month",
        count: Math.round(186 * baseMultiplier),
        percentage: 32.9,
        avgSpend: 280,
        avgTransactionValue: 95,
        retentionRate: 45,
        frequency: "Quarterly"
      },
      {
        segment: "New/Inactive",
        description: "₵0-₵99 per month",
        count: Math.round(88 * baseMultiplier),
        percentage: 15.5,
        avgSpend: 45,
        avgTransactionValue: 35,
        retentionRate: 25,
        frequency: "Rarely"
      },
    ],

    // Customer Segments by Purchase Frequency
    frequencySegments: [
      {
        segment: "Frequent",
        description: "3+ times per week",
        count: Math.round(45 * baseMultiplier),
        percentage: 8.0,
        avgSpend: 4200,
        transactions: Math.round(156 * baseMultiplier),
      },
      {
        segment: "Regular",
        description: "1-2 times per week",
        count: Math.round(134 * baseMultiplier),
        percentage: 23.7,
        avgSpend: 2800,
        transactions: Math.round(89 * baseMultiplier),
      },
      {
        segment: "Occasional",
        description: "2-3 times per month",
        count: Math.round(245 * baseMultiplier),
        percentage: 43.3,
        avgSpend: 1200,
        transactions: Math.round(34 * baseMultiplier),
      },
      {
        segment: "Rare",
        description: "Once per month or less",
        count: Math.round(142 * baseMultiplier),
        percentage: 25.0,
        avgSpend: 450,
        transactions: Math.round(12 * baseMultiplier),
      },
    ],

    // Segment Performance Radar Data
    segmentRadarData: [
      {
        segment: "Premium",
        spending: 95,
        frequency: 90,
        retention: 95,
        satisfaction: 92,
        growth: 85,
      },
      {
        segment: "High Value",
        spending: 75,
        frequency: 70,
        retention: 88,
        satisfaction: 85,
        growth: 78,
      },
      {
        segment: "Regular",
        spending: 55,
        frequency: 50,
        retention: 72,
        satisfaction: 75,
        growth: 65,
      },
      {
        segment: "Occasional",
        spending: 35,
        frequency: 30,
        retention: 45,
        satisfaction: 60,
        growth: 40,
      },
    ],

    // Revenue by Segment
    revenueBySegment: [
      { segment: "Premium", revenue: Math.round(187500 * baseMultiplier), percentage: 42.3 },
      { segment: "High Value", revenue: Math.round(204800 * baseMultiplier), percentage: 46.2 },
      { segment: "Regular", revenue: Math.round(243600 * baseMultiplier), percentage: 55.0 },
      { segment: "Occasional", revenue: Math.round(52080 * baseMultiplier), percentage: 11.8 },
      { segment: "New/Inactive", revenue: Math.round(3960 * baseMultiplier), percentage: 0.9 },
    ],
  };
};

export default function CustomerSegmentationPage() {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<FilterPeriod>("30days");

  if (!user || user.role !== 'branch_admin') {
    return <div>Access denied. Branch Admin role required.</div>;
  }

  // Generate data based on selected filter period
  const data = generateSegmentationData(selectedPeriod);

  const getSegmentBadge = (segment: string) => {
    switch (segment) {
      case "Premium":
        return <Badge className="bg-purple-600">{segment}</Badge>;
      case "High Value":
        return <Badge className="bg-blue-600">{segment}</Badge>;
      case "Regular":
        return <Badge className="bg-green-600">{segment}</Badge>;
      case "Occasional":
        return <Badge className="bg-yellow-600">{segment}</Badge>;
      case "New/Inactive":
        return <Badge variant="outline">{segment}</Badge>;
      default:
        return <Badge>{segment}</Badge>;
    }
  };

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Customer Segmentation Analysis</h2>
        <p className="text-muted-foreground">
          Group customers based on behavior patterns for targeted marketing strategies
        </p>
      </div>

      <FilterBar />

      {/* Segmentation KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Total Customers"
          value={data.kpis.totalCustomers.toString()}
          icon={Users}
          trend={{ value: 8.2, isPositive: true }}
        />
        <KpiCard
          title="Active Segments"
          value={data.kpis.activeSegments.toString()}
          icon={PieChartIcon}
          trend={{ value: 0, isPositive: true }}
        />
        <KpiCard
          title="High Value Customers"
          value={data.kpis.highValueCustomers.toString()}
          icon={Star}
          trend={{ value: 12.5, isPositive: true }}
        />
        <KpiCard
          title="Avg. Segment Value"
          value={formatCurrency(data.kpis.avgSegmentValue)}
          icon={Target}
          trend={{ value: 6.8, isPositive: true }}
        />
      </div>

      {/* Segmentation Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Spending Level Segments */}
        <ChartContainer title="Customer Segments by Spending Level">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data.spendingSegments}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ segment, percentage }) => `${segment}: ${percentage}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="percentage"
              >
                {data.spendingSegments.map((_, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>

        {/* Purchase Frequency Segments */}
        <ChartContainer title="Customer Segments by Purchase Frequency">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data.frequencySegments}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="segment" />
              <YAxis />
              <Tooltip formatter={(value, name) => {
                if (name === "avgSpend") return [formatCurrency(Number(value)), "Avg Spend"];
                return [value, name];
              }} />
              <Legend />
              <Bar dataKey="count" fill={COLORS[0]} name="Customer Count" />
              <Bar dataKey="avgSpend" fill={COLORS[1]} name="Avg Spend" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Revenue Distribution */}
      <ChartContainer title="Revenue Distribution by Customer Segment">
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data.revenueBySegment}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="segment" />
            <YAxis />
            <Tooltip formatter={(value) => [formatCurrency(Number(value)), "Revenue"]} />
            <Bar dataKey="revenue" fill={COLORS[0]} name="Revenue" />
          </BarChart>
        </ResponsiveContainer>
      </ChartContainer>

      {/* Segment Performance Radar */}
      <ChartContainer title="Segment Performance Analysis">
        <ResponsiveContainer width="100%" height={400}>
          <RadarChart data={data.segmentRadarData}>
            <PolarGrid />
            <PolarAngleAxis dataKey="segment" />
            <PolarRadiusAxis angle={90} domain={[0, 100]} />
            <Radar name="Spending" dataKey="spending" stroke={COLORS[0]} fill={COLORS[0]} fillOpacity={0.1} />
            <Radar name="Frequency" dataKey="frequency" stroke={COLORS[1]} fill={COLORS[1]} fillOpacity={0.1} />
            <Radar name="Retention" dataKey="retention" stroke={COLORS[2]} fill={COLORS[2]} fillOpacity={0.1} />
            <Radar name="Satisfaction" dataKey="satisfaction" stroke={COLORS[3]} fill={COLORS[3]} fillOpacity={0.1} />
            <Tooltip />
            <Legend />
          </RadarChart>
        </ResponsiveContainer>
      </ChartContainer>

      {/* Detailed Segment Analysis Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <PieChartIcon className="h-5 w-5" />
            <span>Detailed Customer Segment Analysis</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            title="Customer Segments by Spending Level"
            columns={[
              { 
                key: "segment", 
                title: "Segment", 
                render: (value) => getSegmentBadge(value)
              },
              { key: "description", title: "Description" },
              { key: "count", title: "Customers" },
              { key: "percentage", title: "% of Total" },
              { key: "avgSpend", title: "Avg. Spend" },
              { key: "avgTransactionValue", title: "Avg. Transaction" },
              { key: "retentionRate", title: "Retention %" },
              { key: "frequency", title: "Visit Frequency" },
            ]}
            data={data.spendingSegments.map(segment => ({
              ...segment,
              avgSpend: formatCurrency(segment.avgSpend),
              avgTransactionValue: formatCurrency(segment.avgTransactionValue),
              percentage: `${segment.percentage}%`,
              retentionRate: `${segment.retentionRate}%`
            }))}
          />
        </CardContent>
      </Card>
    </div>
  );
}
