"use client";

import {
  Building,
  Users,
  TrendingUp,
  DollarSign,
  Clock,
  AlertTriangle,
  Star,
  Activity
} from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Legend,
  AreaChart,
  Area
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

// Mock data for Merchant Admin dashboard
const branchPerformanceData = [
  { name: "Downtown Branch", transactions: 450, revenue: 125000, score: 92, efficiency: 85 },
  { name: "Uptown Branch", transactions: 380, revenue: 98000, score: 88, efficiency: 82 },
  { name: "Mall Branch", transactions: 520, revenue: 142000, score: 95, efficiency: 90 },
  { name: "Airport Branch", transactions: 290, revenue: 78000, score: 78, efficiency: 75 },
];

const tellerPerformanceData = [
  { name: "John Smith", branch: "Downtown", transactions: 89, avgValue: 278, score: 94 },
  { name: "Sarah Johnson", branch: "Downtown", transactions: 76, avgValue: 312, score: 91 },
  { name: "Mike Brown", branch: "Uptown", transactions: 82, avgValue: 258, score: 88 },
  { name: "Lisa Davis", branch: "Mall", transactions: 95, avgValue: 273, score: 96 },
];

const customerBehaviorData = [
  { hour: "9AM", newCustomers: 12, returningCustomers: 45, avgTransaction: 245 },
  { hour: "11AM", newCustomers: 18, returningCustomers: 67, avgTransaction: 289 },
  { hour: "1PM", newCustomers: 25, returningCustomers: 89, avgTransaction: 312 },
  { hour: "3PM", newCustomers: 22, returningCustomers: 78, avgTransaction: 298 },
  { hour: "5PM", newCustomers: 15, returningCustomers: 56, avgTransaction: 267 },
];

const peakTransactionPeriods = [
  { day: "Monday", morning: 45, afternoon: 78, evening: 34 },
  { day: "Tuesday", morning: 52, afternoon: 82, evening: 38 },
  { day: "Wednesday", morning: 48, afternoon: 85, evening: 41 },
  { day: "Thursday", morning: 55, afternoon: 92, evening: 45 },
  { day: "Friday", morning: 62, afternoon: 105, evening: 67 },
  { day: "Saturday", morning: 38, afternoon: 95, evening: 78 },
  { day: "Sunday", morning: 25, afternoon: 67, evening: 45 },
];

const customerSegmentationData = [
  { name: "High Value", value: 35, count: 156, avgSpend: 450 },
  { name: "Regular", value: 45, count: 203, avgSpend: 280 },
  { name: "New", value: 15, count: 67, avgSpend: 180 },
  { name: "At Risk", value: 5, count: 23, avgSpend: 120 },
];

const topProductsData = [
  { product: "Mobile Top-up", sales: 1250, revenue: 62500 },
  { product: "Bill Payment", sales: 890, revenue: 44500 },
  { product: "Money Transfer", sales: 650, revenue: 32500 },
  { product: "Account Opening", sales: 420, revenue: 21000 },
];

const highValueCustomers = [
  { name: "David Wilson", totalSpend: 4150, transactions: 18, lastVisit: "2023-05-20", branch: "Mall" },
  { name: "Emma Thompson", totalSpend: 3890, transactions: 15, lastVisit: "2023-05-19", branch: "Downtown" },
  { name: "Robert Chen", totalSpend: 3650, transactions: 12, lastVisit: "2023-05-18", branch: "Uptown" },
  { name: "Maria Garcia", totalSpend: 3420, transactions: 14, lastVisit: "2023-05-17", branch: "Airport" },
];

export default function MerchantAdminDashboard() {
  const { user, currentScope } = useAuth();

  if (!user || user.role !== 'merchant_admin') {
    return <div>Access denied. Merchant Admin role required.</div>;
  }

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Merchant Admin Dashboard</h2>
        <p className="text-muted-foreground">
          All branches, tellers, and transactions under your management
        </p>
      </div>

      <FilterBar />

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Total Branches"
          value="4"
          icon={Building}
          trend={{ value: 0, isPositive: true }}
        />
        <KpiCard
          title="Active Tellers"
          value="16"
          icon={Users}
          trend={{ value: 6.3, isPositive: true }}
        />
        <KpiCard
          title="Total Transactions"
          value="1,640"
          icon={Activity}
          trend={{ value: 12.5, isPositive: true }}
        />
        <KpiCard
          title="Total Revenue"
          value="₵443,000"
          icon={DollarSign}
          trend={{ value: 8.7, isPositive: true }}
        />
      </div>

      {/* Branch Performance Ranking */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Branch Performance Ranking">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={branchPerformanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="score" fill="#0088FE" name="Performance Score" />
              <Bar dataKey="efficiency" fill="#00C49F" name="Efficiency %" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Staff Efficiency Metrics">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={tellerPerformanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
              <YAxis />
              <Tooltip />
              <Bar dataKey="score" fill="#8884D8" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Customer Behavior Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Customer Behavior Analysis">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={customerBehaviorData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="hour" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Area type="monotone" dataKey="newCustomers" stackId="1" stroke="#0088FE" fill="#0088FE" name="New Customers" />
              <Area type="monotone" dataKey="returningCustomers" stackId="1" stroke="#00C49F" fill="#00C49F" name="Returning Customers" />
            </AreaChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Peak Transaction Periods">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={peakTransactionPeriods}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="day" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="morning" fill="#0088FE" name="Morning" />
              <Bar dataKey="afternoon" fill="#00C49F" name="Afternoon" />
              <Bar dataKey="evening" fill="#FFBB28" name="Evening" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Customer Segmentation */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Customer Segmentation">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={customerSegmentationData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {customerSegmentationData.map((_, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Product/Service Insights">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={topProductsData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="product" angle={-45} textAnchor="end" height={80} />
              <YAxis />
              <Tooltip />
              <Bar dataKey="sales" fill="#0088FE" name="Sales Count" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* High-Value Customer Identification */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Star className="h-5 w-5" />
            <span>High-Value Customer Identification</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
          title="High-Value Customers"
            columns={[
              { key: "name", title: "Customer Name" },
              { key: "totalSpend", title: "Total Spend" },
              { key: "transactions", title: "Transactions" },
              { key: "lastVisit", title: "Last Visit" },
              { key: "branch", title: "Preferred Branch" },
            ]}
            data={highValueCustomers}
          />
        </CardContent>
      </Card>

      {/* Anomaly Detection Alerts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5" />
            <span>Anomaly Detection Alerts</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <div className="font-medium">Unusual transaction pattern detected</div>
                <div className="text-sm text-muted-foreground">Downtown Branch - Multiple high-value transactions</div>
              </div>
              <Badge variant="destructive">High</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <div className="font-medium">Staff efficiency drop</div>
                <div className="text-sm text-muted-foreground">Airport Branch - 15% decrease in processing speed</div>
              </div>
              <Badge variant="secondary">Medium</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <div className="font-medium">Customer behavior change</div>
                <div className="text-sm text-muted-foreground">Mall Branch - Shift in peak hours</div>
              </div>
              <Badge variant="outline">Low</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
