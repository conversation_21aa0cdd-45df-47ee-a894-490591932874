/**
 * Currency formatting utilities for Ghana Cedis (GHS)
 */

export function formatCurrency(amount: number, options?: {
  showSymbol?: boolean;
  decimals?: number;
}): string {
  const { showSymbol = true, decimals = 2 } = options || {};
  
  const formatted = amount.toLocaleString('en-GH', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });
  
  return showSymbol ? `₵${formatted}` : formatted;
}

export function formatCurrencyShort(amount: number): string {
  if (amount >= 1000000) {
    return `₵${(amount / 1000000).toFixed(1)}M`;
  } else if (amount >= 1000) {
    return `₵${(amount / 1000).toFixed(1)}K`;
  } else {
    return formatCurrency(amount);
  }
}

export const CURRENCY_SYMBOL = '₵';
export const CURRENCY_CODE = 'GHS';
export const CURRENCY_NAME = 'Ghana Cedi';
