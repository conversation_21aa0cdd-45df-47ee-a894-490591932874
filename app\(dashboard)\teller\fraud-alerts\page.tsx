"use client";

import { useState } from "react";
import {
  <PERSON>ert<PERSON><PERSON>gle,
  Shield,
  Eye,
  Clock,
  User,
  DollarSign,
  CheckCircle,
  XCircle,
  AlertCircle
} from "lucide-react";

import { useAuth } from "@/lib/auth/context";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DataTable } from "@/components/dashboard/DataTable";
import { formatCurrency } from "@/lib/utils/currency";

// Mock fraud alerts data
const fraudAlerts = [
  {
    id: "FA001",
    type: "Duplicate Payment",
    customer: "<PERSON>",
    customerPhone: "+233 24 123 4567",
    amount: 500,
    time: "10:30 AM",
    date: "2024-01-20",
    risk: "High",
    status: "Active",
    description: "Customer attempting same payment twice within 5 minutes",
    action: "Verify with customer before processing"
  },
  {
    id: "FA002",
    type: "Unusual Pattern",
    customer: "<PERSON>",
    customerPhone: "+233 24 234 5678",
    amount: 1200,
    time: "2:15 PM",
    date: "2024-01-20",
    risk: "Medium",
    status: "Under Review",
    description: "Transaction amount 300% higher than customer's usual pattern",
    action: "Request additional verification documents"
  },
  {
    id: "FA003",
    type: "Multiple Attempts",
    customer: "Bob Johnson",
    customerPhone: "+233 24 345 6789",
    amount: 300,
    time: "4:45 PM",
    date: "2024-01-19",
    risk: "Low",
    status: "Resolved",
    description: "Customer made 5 failed PIN attempts before successful transaction",
    action: "Customer verified identity successfully"
  },
  {
    id: "FA004",
    type: "Off-Hours Transaction",
    customer: "Mary Wilson",
    customerPhone: "+233 24 456 7890",
    amount: 2500,
    time: "11:45 PM",
    date: "2024-01-19",
    risk: "High",
    status: "Escalated",
    description: "Large transaction attempted outside normal business hours",
    action: "Escalated to security team for investigation"
  },
  {
    id: "FA005",
    type: "New Device",
    customer: "David Brown",
    customerPhone: "+233 24 567 8901",
    amount: 150,
    time: "9:20 AM",
    date: "2024-01-20",
    risk: "Medium",
    status: "Active",
    description: "Transaction from unrecognized device/location",
    action: "Verify customer identity and device ownership"
  }
];

const securityTips = [
  {
    title: "Verify Customer Identity",
    description: "Always check ID documents for transactions above ₵1000",
    icon: User,
    priority: "High"
  },
  {
    title: "Watch for Nervous Behavior",
    description: "Be alert to customers who seem anxious or rushed",
    icon: Eye,
    priority: "Medium"
  },
  {
    title: "Check Transaction Patterns",
    description: "Question unusual amounts or frequency for regular customers",
    icon: AlertTriangle,
    priority: "High"
  },
  {
    title: "Secure PIN Entry",
    description: "Ensure customers shield their PIN and don't share it",
    icon: Shield,
    priority: "High"
  }
];

export default function FraudAlertsPage() {
  const { user } = useAuth();
  const [selectedAlert, setSelectedAlert] = useState<string | null>(null);

  if (!user || user.role !== 'teller') {
    return <div>Access denied. Teller role required.</div>;
  }

  const getRiskBadge = (risk: string) => {
    switch (risk) {
      case "High":
        return <Badge variant="destructive">{risk}</Badge>;
      case "Medium":
        return <Badge className="bg-yellow-600">{risk}</Badge>;
      case "Low":
        return <Badge variant="outline">{risk}</Badge>;
      default:
        return <Badge>{risk}</Badge>;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-red-600">{status}</Badge>;
      case "Under Review":
        return <Badge className="bg-yellow-600">{status}</Badge>;
      case "Resolved":
        return <Badge className="bg-green-600">{status}</Badge>;
      case "Escalated":
        return <Badge className="bg-purple-600">{status}</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  const activeAlerts = fraudAlerts.filter(alert => alert.status === "Active");
  const highRiskAlerts = fraudAlerts.filter(alert => alert.risk === "High");

  return (
    <div className="w-full max-w-full overflow-hidden space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Fraud Alerts & Security</h2>
        <p className="text-muted-foreground">
          Real-time fraud detection and security guidelines for safe transactions
        </p>
      </div>

      {/* Alert Summary Cards */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Alerts</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{activeAlerts.length}</div>
            <p className="text-xs text-muted-foreground">
              Require immediate attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High Risk</CardTitle>
            <Shield className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{highRiskAlerts.length}</div>
            <p className="text-xs text-muted-foreground">
              High priority cases
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Alerts</CardTitle>
            <Clock className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {fraudAlerts.filter(alert => alert.date === "2024-01-20").length}
            </div>
            <p className="text-xs text-muted-foreground">
              Alerts generated today
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Resolved</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {fraudAlerts.filter(alert => alert.status === "Resolved").length}
            </div>
            <p className="text-xs text-muted-foreground">
              Successfully handled
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Active Fraud Alerts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <span>Active Fraud Alerts</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {activeAlerts.length > 0 ? (
            <div className="space-y-4">
              {activeAlerts.map((alert) => (
                <div key={alert.id} className="p-4 border border-red-200 rounded-lg bg-red-50 space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-2">
                      <AlertCircle className="h-5 w-5 text-red-500" />
                      <h4 className="font-medium">{alert.type}</h4>
                      {getRiskBadge(alert.risk)}
                      {getStatusBadge(alert.status)}
                    </div>
                    <div className="text-right text-sm text-muted-foreground">
                      <div>{alert.time}</div>
                      <div>{alert.date}</div>
                    </div>
                  </div>
                  
                  <div className="grid gap-2 md:grid-cols-2">
                    <div>
                      <strong>Customer:</strong> {alert.customer}
                    </div>
                    <div>
                      <strong>Amount:</strong> {formatCurrency(alert.amount)}
                    </div>
                    <div>
                      <strong>Phone:</strong> {alert.customerPhone}
                    </div>
                    <div>
                      <strong>Alert ID:</strong> {alert.id}
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div><strong>Description:</strong> {alert.description}</div>
                    <div className="p-2 bg-yellow-50 border border-yellow-200 rounded">
                      <strong>Recommended Action:</strong> {alert.action}
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline">
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Mark Resolved
                    </Button>
                    <Button size="sm" variant="outline">
                      <AlertTriangle className="h-4 w-4 mr-2" />
                      Escalate
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
              <p>No active fraud alerts. All clear!</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* All Alerts Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>All Fraud Alerts</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            title="Fraud Alert History"
            columns={[
              { key: "id", title: "Alert ID" },
              { key: "type", title: "Type" },
              { key: "customer", title: "Customer" },
              { key: "amount", title: "Amount" },
              { key: "time", title: "Time" },
              { key: "date", title: "Date" },
              { 
                key: "risk", 
                title: "Risk Level", 
                render: (value) => getRiskBadge(value)
              },
              { 
                key: "status", 
                title: "Status", 
                render: (value) => getStatusBadge(value)
              },
            ]}
            data={fraudAlerts.map(alert => ({
              ...alert,
              amount: formatCurrency(alert.amount)
            }))}
          />
        </CardContent>
      </Card>

      {/* Security Tips */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Security Guidelines</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            {securityTips.map((tip, index) => (
              <div key={index} className="p-4 border rounded-lg space-y-2">
                <div className="flex items-center space-x-2">
                  <tip.icon className="h-5 w-5 text-blue-600" />
                  <h4 className="font-medium">{tip.title}</h4>
                  <Badge variant={tip.priority === "High" ? "destructive" : "secondary"}>
                    {tip.priority}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">{tip.description}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
