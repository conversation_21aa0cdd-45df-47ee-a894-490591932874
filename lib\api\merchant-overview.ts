import { ApiClient, withRetry } from './client';
import {
  MerchantOverviewRequest,
  MerchantOverviewResponse,
  ApiError,
} from './types';

// Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://54.246.247.31:8000';

// Merchant Overview API Service
export class MerchantOverviewApiService {
  
  // Get merchant overview data
  static async getMerchantOverview(params: MerchantOverviewRequest): Promise<MerchantOverviewResponse> {
    try {
      const { merchantId, ...queryParams } = params;
      
      // Build query string
      const searchParams = new URLSearchParams();
      
      if (queryParams.topMode) searchParams.append('top_mode', queryParams.topMode);
      if (queryParams.topLimit) searchParams.append('top_limit', queryParams.topLimit.toString());
      if (queryParams.startDate) searchParams.append('start_date', queryParams.startDate);
      if (queryParams.endDate) searchParams.append('end_date', queryParams.endDate);
      if (queryParams.rangeDays) searchParams.append('range_days', queryParams.rangeDays.toString());
      
      const url = `${API_BASE_URL}/merchants/${merchantId}/overview?${searchParams.toString()}`;
      
      console.log('🔄 Fetching merchant overview from:', url);
      
      const response = await withRetry(() =>
        fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
        })
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API Error:', response.status, errorText);
        throw new Error(`API Error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('✅ Merchant overview data received:', data);
      
      return data;
    } catch (error) {
      console.error('❌ Failed to fetch merchant overview:', error);
      throw error;
    }
  }

  // Helper method to format date for API
  static formatDateForAPI(date: Date): string {
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const year = date.getFullYear();
    return `${month}%2F${day}%2F${year}`;
  }

  // Helper method to get date range for common periods
  static getDateRangeForPeriod(period: '7days' | '30days' | '90days' | 'custom', customStart?: Date, customEnd?: Date): { startDate?: string; endDate?: string; rangeDays?: number } {
    const now = new Date();
    
    switch (period) {
      case '7days':
        return { rangeDays: 7 };
      case '30days':
        return { rangeDays: 30 };
      case '90days':
        return { rangeDays: 90 };
      case 'custom':
        if (customStart && customEnd) {
          return {
            startDate: this.formatDateForAPI(customStart),
            endDate: this.formatDateForAPI(customEnd)
          };
        }
        return { rangeDays: 30 }; // fallback
      default:
        return { rangeDays: 30 };
    }
  }

  // Transform API response to chart-friendly format
  static transformToChartData(response: MerchantOverviewResponse) {
    return {
      // Transaction volume chart data
      transactionVolume: {
        labels: response.transaction_volume.data.labels,
        datasets: [{
          label: 'Transaction Volume',
          data: response.transaction_volume.data.values,
          backgroundColor: '#1D4987',
          borderColor: '#1D4987',
        }]
      },

      // Transaction count chart data
      transactionCount: {
        labels: response.transaction_count.data.labels,
        datasets: [{
          label: 'Transaction Count',
          data: response.transaction_count.data.values,
          backgroundColor: '#00C49F',
          borderColor: '#00C49F',
        }]
      },

      // Average transaction value chart data
      averageTransactionValue: {
        labels: response.average_transactions.data.labels,
        datasets: [{
          label: 'Average Transaction Value',
          data: response.average_transactions.data.values,
          backgroundColor: '#FFBB28',
          borderColor: '#FFBB28',
        }]
      },

      // Customer segmentation pie chart data
      customerSegmentation: [
        {
          name: 'High Value',
          value: response.segmentation.data.high_value.length,
          amount: response.segmentation.data.high_value.reduce((sum, item) => sum + item.amount, 0)
        },
        {
          name: 'Mid Value',
          value: response.segmentation.data.mid_value.length,
          amount: response.segmentation.data.mid_value.reduce((sum, item) => sum + item.amount, 0)
        },
        {
          name: 'Low Value',
          value: response.segmentation.data.low_value.length,
          amount: response.segmentation.data.low_value.reduce((sum, item) => sum + item.amount, 0)
        }
      ],

      // Top customers table data
      topCustomers: response.top_customers.data.map(customer => ({
        customerId: customer.customer_id,
        merchantId: customer.merchant_id,
        amount: customer.amount,
        formattedAmount: `₵${customer.amount.toFixed(2)}`
      })),

      // Transaction outliers
      outliers: response.transaction_outliers.data.filter(item => item.outlier),

      // Days between transactions analysis
      transactionFrequency: this.analyzeTransactionFrequency(response.days_between_transactions.data)
    };
  }

  // Analyze transaction frequency patterns
  private static analyzeTransactionFrequency(data: any[]) {
    const frequencyMap: Record<string, number[]> = {};
    
    data.forEach(item => {
      if (item.days_since !== null) {
        if (!frequencyMap[item.customer_id]) {
          frequencyMap[item.customer_id] = [];
        }
        frequencyMap[item.customer_id].push(item.days_since);
      }
    });

    const analysis = Object.entries(frequencyMap).map(([customerId, daysBetween]) => {
      const avgDays = daysBetween.reduce((sum, days) => sum + days, 0) / daysBetween.length;
      const frequency = avgDays <= 3 ? 'High' : avgDays <= 7 ? 'Medium' : 'Low';
      
      return {
        customerId,
        averageDaysBetween: Math.round(avgDays),
        frequency,
        transactionCount: daysBetween.length + 1 // +1 for the first transaction
      };
    });

    return analysis.sort((a, b) => a.averageDaysBetween - b.averageDaysBetween);
  }

  // Calculate KPIs from the response
  static calculateKPIs(response: MerchantOverviewResponse) {
    const totalCustomers = response.segmentation.data.high_value.length + 
                          response.segmentation.data.mid_value.length + 
                          response.segmentation.data.low_value.length;

    const totalRevenue = response.segmentation.data.high_value.reduce((sum, item) => sum + item.amount, 0) +
                        response.segmentation.data.mid_value.reduce((sum, item) => sum + item.amount, 0) +
                        response.segmentation.data.low_value.reduce((sum, item) => sum + item.amount, 0);

    const totalTransactions = response.transaction_count.data.values.reduce((sum, val) => sum + val, 0);
    const avgTransactionValue = totalTransactions > 0 ? totalRevenue / totalTransactions : 0;

    return {
      totalCustomers,
      totalRevenue,
      totalTransactions,
      avgTransactionValue,
      highValueCustomers: response.segmentation.data.high_value.length,
      outlierCount: response.transaction_outliers.data.filter(item => item.outlier).length
    };
  }
}
