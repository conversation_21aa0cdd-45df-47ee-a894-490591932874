"use client";

import {
  Activity,
  Clock,
  TrendingUp,
  BarChart3,
  Target,
  Repeat
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  AreaChart,
  Area,
  Composed<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { formatCurrency } from "@/lib/utils/currency";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

// Transaction Volume Data
const transactionVolumeData = [
  { date: "Jan 15", daily: 156, weekly: 892, monthly: 3420 },
  { date: "Jan 16", daily: 142, weekly: 898, monthly: 3562 },
  { date: "Jan 17", daily: 178, weekly: 945, monthly: 3740 },
  { date: "Jan 18", daily: 165, weekly: 967, monthly: 3905 },
  { date: "Jan 19", daily: 189, weekly: 1024, monthly: 4094 },
  { date: "Jan 20", daily: 203, weekly: 1089, monthly: 4297 },
  { date: "Jan 21", daily: 176, weekly: 1134, monthly: 4473 },
];

// Peak transaction hours
const peakTransactionHours = [
  { hour: "8AM", transactions: 45, efficiency: 92 },
  { hour: "9AM", transactions: 78, efficiency: 88 },
  { hour: "10AM", transactions: 95, efficiency: 85 },
  { hour: "11AM", transactions: 112, efficiency: 82 },
  { hour: "12PM", transactions: 134, efficiency: 78 },
  { hour: "1PM", transactions: 156, efficiency: 75 },
  { hour: "2PM", transactions: 142, efficiency: 80 },
  { hour: "3PM", transactions: 128, efficiency: 83 },
  { hour: "4PM", transactions: 98, efficiency: 87 },
  { hour: "5PM", transactions: 67, efficiency: 90 },
];

// Average Transaction Value Data
const avgTransactionValueData = [
  { period: "Jan Week 1", value: 234, trend: 5.2 },
  { period: "Jan Week 2", value: 245, trend: 4.7 },
  { period: "Jan Week 3", value: 238, trend: -2.9 },
  { period: "Jan Week 4", value: 252, trend: 5.9 },
  { period: "Feb Week 1", value: 267, trend: 6.0 },
];

// Monthly comparison data
const monthlyComparisonData = [
  { month: "Sep", avgValue: 198, totalTransactions: 2340, totalRevenue: 463320 },
  { month: "Oct", avgValue: 212, totalTransactions: 2567, totalRevenue: 544204 },
  { month: "Nov", avgValue: 225, totalTransactions: 2789, totalRevenue: 627525 },
  { month: "Dec", avgValue: 241, totalTransactions: 3012, totalRevenue: 725892 },
  { month: "Jan", avgValue: 256, totalTransactions: 3245, totalRevenue: 830720 },
];

// Return frequency trends
const returnFrequencyTrends = [
  { month: "Sep", daily: 12, weekly: 35, monthly: 28, quarterly: 8 },
  { month: "Oct", daily: 15, weekly: 38, monthly: 25, quarterly: 12 },
  { month: "Nov", daily: 18, weekly: 42, monthly: 22, quarterly: 15 },
  { month: "Dec", daily: 22, weekly: 45, monthly: 20, quarterly: 18 },
  { month: "Jan", daily: 25, weekly: 42, monthly: 22, quarterly: 16 },
];

export default function BranchAdminTransactionsPage() {
  const { user } = useAuth();

  if (!user || user.role !== 'branch_admin') {
    return <div>Access denied. Branch Admin role required.</div>;
  }

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Transaction Analysis</h2>
        <p className="text-muted-foreground">
          Comprehensive analysis of transaction volumes, patterns, and average values
        </p>
      </div>

      <FilterBar />

      {/* Transaction KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Daily Transactions"
          value="873"
          icon={Activity}
          trend={{ value: 12.5, isPositive: true }}
        />
        <KpiCard
          title="Avg. Transaction Value"
          value={formatCurrency(256)}
          icon={Target}
          trend={{ value: 6.0, isPositive: true }}
        />
        <KpiCard
          title="Peak Hour"
          value="1:00 PM"
          icon={Clock}
          trend={{ value: 8.3, isPositive: true }}
        />
        <KpiCard
          title="Monthly Growth"
          value="+12.5%"
          icon={TrendingUp}
          trend={{ value: 12.5, isPositive: true }}
        />
      </div>

      {/* Main Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Transaction Volume Trends */}
        <ChartContainer title="Transaction Volume Trends">
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart data={transactionVolumeData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="daily" fill={COLORS[0]} name="Daily Volume" />
              <Line type="monotone" dataKey="weekly" stroke={COLORS[1]} strokeWidth={2} name="Weekly Trend" />
            </ComposedChart>
          </ResponsiveContainer>
        </ChartContainer>

        {/* Peak Transaction Hours */}
        <ChartContainer title="Peak Transaction Hours & Efficiency">
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart data={peakTransactionHours}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="hour" />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip />
              <Bar yAxisId="left" dataKey="transactions" fill={COLORS[0]} name="Transactions" />
              <Line yAxisId="right" type="monotone" dataKey="efficiency" stroke={COLORS[1]} strokeWidth={2} name="Efficiency %" />
            </ComposedChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Average Transaction Value Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Average Transaction Value Trends">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={avgTransactionValueData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="period" />
              <YAxis />
              <Tooltip formatter={(value) => [formatCurrency(Number(value)), "Avg Value"]} />
              <Line type="monotone" dataKey="value" stroke={COLORS[0]} strokeWidth={3} dot={{ r: 6 }} />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Monthly Performance Comparison">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={monthlyComparisonData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value, name) => {
                if (name === "avgValue") return [formatCurrency(Number(value)), "Avg Value"];
                if (name === "totalRevenue") return [formatCurrency(Number(value)), "Total Revenue"];
                return [value, name];
              }} />
              <Bar dataKey="avgValue" fill={COLORS[0]} name="Avg Value" />
              <Bar dataKey="totalTransactions" fill={COLORS[1]} name="Transactions" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Return Frequency Analysis */}
      <ChartContainer title="Customer Return Frequency Trends">
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={returnFrequencyTrends}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Area type="monotone" dataKey="daily" stackId="1" stroke={COLORS[0]} fill={COLORS[0]} name="Daily Returners" />
            <Area type="monotone" dataKey="weekly" stackId="1" stroke={COLORS[1]} fill={COLORS[1]} name="Weekly Returners" />
            <Area type="monotone" dataKey="monthly" stackId="1" stroke={COLORS[2]} fill={COLORS[2]} name="Monthly Returners" />
          </AreaChart>
        </ResponsiveContainer>
      </ChartContainer>

      {/* Transaction Insights Cards */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Peak Hour</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1:00 PM</div>
            <p className="text-xs text-muted-foreground">
              156 transactions
            </p>
            <Progress value={75} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Most Frequent Returners</CardTitle>
            <Repeat className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4-7 Days</div>
            <p className="text-xs text-muted-foreground">
              31.1% of customers
            </p>
            <Progress value={31} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Transaction Growth</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">+12.5%</div>
            <p className="text-xs text-muted-foreground">
              vs last month
            </p>
            <Progress value={85} className="mt-2" />
          </CardContent>
        </Card>
      </div>

      {/* Transaction Volume vs Value Scatter */}
      <ChartContainer title="Transaction Volume vs Average Value Correlation">
        <ResponsiveContainer width="100%" height={300}>
          <ScatterChart data={monthlyComparisonData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="totalTransactions" name="Total Transactions" />
            <YAxis dataKey="avgValue" name="Avg Value" />
            <Tooltip formatter={(value, name) => {
              if (name === "avgValue") return [formatCurrency(Number(value)), "Avg Value"];
              return [value, name];
            }} />
            <Scatter dataKey="avgValue" fill={COLORS[0]} />
          </ScatterChart>
        </ResponsiveContainer>
      </ChartContainer>
    </div>
  );
}
