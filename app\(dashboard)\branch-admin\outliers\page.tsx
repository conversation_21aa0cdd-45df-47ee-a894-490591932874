"use client";

import { useState } from "react";
import {
  <PERSON>ert<PERSON><PERSON>gle,
  Shield,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Activity,
  Eye,
  Clock
} from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  <PERSON>att<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ReferenceLine
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatCurrency } from "@/lib/utils/currency";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

// Filter periods
type FilterPeriod = "7days" | "30days" | "90days" | "custom";

// Mock data generator for outlier detection
const generateOutlierData = (period: FilterPeriod) => {
  const baseMultiplier = period === "7days" ? 0.3 : period === "30days" ? 1 : period === "90days" ? 2.5 : 1;
  
  return {
    // KPI Data
    kpis: {
      totalOutliers: Math.round(23 * baseMultiplier),
      highValueOutliers: Math.round(8 * baseMultiplier),
      lowValueOutliers: Math.round(15 * baseMultiplier),
      suspiciousTransactions: Math.round(5 * baseMultiplier),
      avgTransactionValue: 256,
      outlierThreshold: 1000, // Transactions above this are flagged
    },

    // Suspicious Transactions
    suspiciousTransactions: [
      {
        id: "TXN001",
        amount: 15000,
        customer: "Unknown Customer",
        phone: "+233 24 999 8888",
        time: "14:23",
        date: "2024-01-20",
        teller: "John Smith",
        flag: "High Value",
        severity: "High",
        reason: "Amount 58x higher than average"
      },
      {
        id: "TXN002",
        amount: 8500,
        customer: "Mary Johnson",
        phone: "+233 24 555 1234",
        time: "09:15",
        date: "2024-01-20",
        teller: "Sarah Johnson",
        flag: "Unusual Pattern",
        severity: "Medium",
        reason: "Multiple large transactions in short period"
      },
      {
        id: "TXN003",
        amount: 12000,
        customer: "David Wilson",
        phone: "+233 24 123 4567",
        time: "16:45",
        date: "2024-01-19",
        teller: "Mike Brown",
        flag: "High Value",
        severity: "High",
        reason: "Amount 47x higher than customer average"
      },
      {
        id: "TXN004",
        amount: 25,
        customer: "Emma Thompson",
        phone: "+233 24 234 5678",
        time: "11:30",
        date: "2024-01-19",
        teller: "Lisa Davis",
        flag: "Low Value",
        severity: "Low",
        reason: "Amount 90% lower than customer average"
      },
      {
        id: "TXN005",
        amount: 18000,
        customer: "Robert Chen",
        phone: "+233 24 345 6789",
        time: "13:20",
        date: "2024-01-18",
        teller: "Tom Wilson",
        flag: "After Hours",
        severity: "Medium",
        reason: "Large transaction outside normal hours"
      },
    ],

    // Transaction Volume Spikes
    volumeSpikes: [
      { hour: "08:00", normal: 45, actual: 45, spike: false },
      { hour: "09:00", normal: 78, actual: 78, spike: false },
      { hour: "10:00", normal: 95, actual: 156, spike: true },
      { hour: "11:00", normal: 112, actual: 112, spike: false },
      { hour: "12:00", normal: 134, actual: 134, spike: false },
      { hour: "13:00", normal: 156, actual: 234, spike: true },
      { hour: "14:00", normal: 142, actual: 142, spike: false },
      { hour: "15:00", normal: 128, actual: 89, spike: true },
      { hour: "16:00", normal: 98, actual: 98, spike: false },
      { hour: "17:00", normal: 67, actual: 67, spike: false },
    ],

    // Transaction Value Distribution
    valueDistribution: [
      { range: "₵0-₵100", count: Math.round(450 * baseMultiplier), normal: true },
      { range: "₵100-₵500", count: Math.round(320 * baseMultiplier), normal: true },
      { range: "₵500-₵1000", count: Math.round(180 * baseMultiplier), normal: true },
      { range: "₵1000-₵5000", count: Math.round(45 * baseMultiplier), normal: false },
      { range: "₵5000-₵10000", count: Math.round(12 * baseMultiplier), normal: false },
      { range: "₵10000+", count: Math.round(8 * baseMultiplier), normal: false },
    ],

    // Daily Outlier Trends
    dailyOutliers: [
      { date: period === "7days" ? "Jan 15" : period === "30days" ? "Week 1" : "Month 1", 
        outliers: Math.round(3 * baseMultiplier), 
        total: Math.round(156 * baseMultiplier) },
      { date: period === "7days" ? "Jan 16" : period === "30days" ? "Week 2" : "Month 2", 
        outliers: Math.round(5 * baseMultiplier), 
        total: Math.round(142 * baseMultiplier) },
      { date: period === "7days" ? "Jan 17" : period === "30days" ? "Week 3" : "Month 3", 
        outliers: Math.round(8 * baseMultiplier), 
        total: Math.round(178 * baseMultiplier) },
      { date: period === "7days" ? "Jan 18" : period === "30days" ? "Week 4" : "Month 4", 
        outliers: Math.round(2 * baseMultiplier), 
        total: Math.round(165 * baseMultiplier) },
      { date: period === "7days" ? "Jan 19" : period === "30days" ? "Current" : "Month 5", 
        outliers: Math.round(5 * baseMultiplier), 
        total: Math.round(189 * baseMultiplier) },
    ],
  };
};

export default function OutlierDetectionPage() {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<FilterPeriod>("30days");

  if (!user || user.role !== 'branch_admin') {
    return <div>Access denied. Branch Admin role required.</div>;
  }

  // Generate data based on selected filter period
  const data = generateOutlierData(selectedPeriod);

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case "High":
        return <Badge variant="destructive">{severity}</Badge>;
      case "Medium":
        return <Badge variant="secondary">{severity}</Badge>;
      case "Low":
        return <Badge variant="outline">{severity}</Badge>;
      default:
        return <Badge>{severity}</Badge>;
    }
  };

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Outlier Detection & Fraud Prevention</h2>
        <p className="text-muted-foreground">
          Identify abnormal transactions and suspicious patterns for enhanced security
        </p>
      </div>

      <FilterBar />

      {/* Outlier KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Total Outliers"
          value={data.kpis.totalOutliers.toString()}
          icon={AlertTriangle}
          trend={{ value: 15.2, isPositive: false }}
        />
        <KpiCard
          title="High Value Outliers"
          value={data.kpis.highValueOutliers.toString()}
          icon={TrendingUp}
          trend={{ value: 8.7, isPositive: false }}
        />
        <KpiCard
          title="Suspicious Transactions"
          value={data.kpis.suspiciousTransactions.toString()}
          icon={Shield}
          trend={{ value: 12.3, isPositive: false }}
        />
        <KpiCard
          title="Detection Rate"
          value="2.1%"
          icon={Eye}
          trend={{ value: 5.8, isPositive: true }}
        />
      </div>

      {/* Charts Section */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Transaction Volume Spikes */}
        <ChartContainer title="Transaction Volume Anomalies">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data.volumeSpikes}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="hour" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="normal" fill={COLORS[1]} name="Normal Volume" />
              <Bar dataKey="actual" fill={COLORS[0]} name="Actual Volume" />
              <ReferenceLine y={150} stroke="red" strokeDasharray="5 5" label="Spike Threshold" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        {/* Value Distribution */}
        <ChartContainer title="Transaction Value Distribution">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data.valueDistribution}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="range" />
              <YAxis />
              <Tooltip />
              <Bar 
                dataKey="count" 
                fill={(entry) => entry.normal ? COLORS[1] : COLORS[3]} 
                name="Transaction Count" 
              />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Daily Outlier Trends */}
      <ChartContainer title="Daily Outlier Detection Trends">
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data.dailyOutliers}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Line type="monotone" dataKey="outliers" stroke={COLORS[3]} strokeWidth={3} name="Outliers Detected" />
            <Line type="monotone" dataKey="total" stroke={COLORS[0]} strokeWidth={2} name="Total Transactions" />
          </LineChart>
        </ResponsiveContainer>
      </ChartContainer>

      {/* Suspicious Transactions Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5" />
            <span>Suspicious Transactions Requiring Investigation</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            title="Flagged Transactions"
            columns={[
              { key: "id", title: "Transaction ID" },
              { key: "amount", title: "Amount" },
              { key: "customer", title: "Customer" },
              { key: "phone", title: "Phone" },
              { key: "time", title: "Time" },
              { key: "date", title: "Date" },
              { key: "teller", title: "Teller" },
              { key: "flag", title: "Flag Type" },
              { 
                key: "severity", 
                title: "Severity", 
                render: (value) => getSeverityBadge(value)
              },
              { key: "reason", title: "Reason" },
            ]}
            data={data.suspiciousTransactions.map(transaction => ({
              ...transaction,
              amount: formatCurrency(transaction.amount)
            }))}
          />
        </CardContent>
      </Card>
    </div>
  );
}
