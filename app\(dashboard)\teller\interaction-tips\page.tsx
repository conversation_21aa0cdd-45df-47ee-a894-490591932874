"use client";

import {
  Users,
  Lightbulb,
  TrendingUp,
  Clock,
  Star,
  MessageCircle,
  CheckCircle,
  AlertCircle
} from "lucide-react";

import { useAuth } from "@/lib/auth/context";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

// Mock data for customer interaction tips
const customerTips = [
  {
    id: 1,
    pattern: "Mobile Top-up + Bill Payment",
    frequency: "78%",
    suggestedPairing: "Insurance Payment",
    tip: "When customers do mobile top-up, 78% also pay bills. Suggest insurance payments to increase transaction value.",
    impact: "High",
    category: "Cross-selling",
    timesSeen: 45,
    successRate: 67
  },
  {
    id: 2,
    pattern: "Morning Rush (8-10 AM)",
    frequency: "65%",
    suggestedPairing: "Quick Services",
    tip: "Morning customers prefer fast transactions. Prioritize mobile top-ups and quick transfers.",
    impact: "Medium",
    category: "Timing",
    timesSeen: 32,
    successRate: 82
  },
  {
    id: 3,
    pattern: "End-of-Month Transactions",
    frequency: "89%",
    suggestedPairing: "Savings Account",
    tip: "End-of-month customers often have salary deposits. Suggest savings account opening or investment products.",
    impact: "High",
    category: "Product Recommendation",
    timesSeen: 28,
    successRate: 45
  },
  {
    id: 4,
    pattern: "Elderly Customers",
    frequency: "92%",
    suggestedPairing: "Assistance",
    tip: "Elderly customers appreciate extra help with forms and explanations. Take time to explain each step.",
    impact: "High",
    category: "Customer Service",
    timesSeen: 56,
    successRate: 95
  },
  {
    id: 5,
    pattern: "Business Account Holders",
    frequency: "71%",
    suggestedPairing: "Business Services",
    tip: "Business customers often need bulk transactions. Offer business banking services and bulk payment options.",
    impact: "Medium",
    category: "Business Development",
    timesSeen: 19,
    successRate: 58
  },
  {
    id: 6,
    pattern: "First-time Visitors",
    frequency: "85%",
    suggestedPairing: "Account Opening",
    tip: "New customers are often interested in opening accounts. Explain benefits of different account types.",
    impact: "High",
    category: "Customer Acquisition",
    timesSeen: 23,
    successRate: 73
  }
];

const serviceInsights = [
  {
    service: "Mobile Top-up",
    frequency: 45,
    avgValue: 25,
    bestTime: "Morning",
    customerType: "Young Adults",
    tip: "Suggest data bundles for higher value"
  },
  {
    service: "Bill Payment",
    frequency: 38,
    avgValue: 85,
    bestTime: "End of Month",
    customerType: "Families",
    tip: "Offer automatic payment setup"
  },
  {
    service: "Money Transfer",
    frequency: 32,
    avgValue: 150,
    bestTime: "Afternoon",
    customerType: "Working Adults",
    tip: "Explain transfer limits and fees upfront"
  }
];

export default function InteractionTipsPage() {
  const { user } = useAuth();

  if (!user || user.role !== 'teller') {
    return <div>Access denied. Teller role required.</div>;
  }

  const getImpactBadge = (impact: string) => {
    switch (impact) {
      case "High":
        return <Badge className="bg-green-600">{impact}</Badge>;
      case "Medium":
        return <Badge className="bg-yellow-600">{impact}</Badge>;
      case "Low":
        return <Badge variant="outline">{impact}</Badge>;
      default:
        return <Badge>{impact}</Badge>;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "Cross-selling":
        return <TrendingUp className="h-4 w-4" />;
      case "Timing":
        return <Clock className="h-4 w-4" />;
      case "Customer Service":
        return <Star className="h-4 w-4" />;
      case "Product Recommendation":
        return <Lightbulb className="h-4 w-4" />;
      case "Business Development":
        return <Users className="h-4 w-4" />;
      case "Customer Acquisition":
        return <MessageCircle className="h-4 w-4" />;
      default:
        return <CheckCircle className="h-4 w-4" />;
    }
  };

  return (
    <div className="w-full max-w-full overflow-hidden space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Customer Interaction Tips</h2>
        <p className="text-muted-foreground">
          AI-powered suggestions to improve customer service and increase sales
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Tips</CardTitle>
            <Lightbulb className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{customerTips.length}</div>
            <p className="text-xs text-muted-foreground">
              Based on your patterns
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Success Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(customerTips.reduce((sum, tip) => sum + tip.successRate, 0) / customerTips.length)}%
            </div>
            <p className="text-xs text-muted-foreground">
              When tips are applied
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High Impact Tips</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {customerTips.filter(tip => tip.impact === "High").length}
            </div>
            <p className="text-xs text-muted-foreground">
              Priority recommendations
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Times Applied</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {customerTips.reduce((sum, tip) => sum + tip.timesSeen, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Total opportunities
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Customer Interaction Tips */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MessageCircle className="h-5 w-5" />
            <span>Personalized Customer Tips</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {customerTips.map((tip) => (
              <div key={tip.id} className="p-4 border rounded-lg space-y-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-2">
                    {getCategoryIcon(tip.category)}
                    <h4 className="font-medium">{tip.pattern}</h4>
                    {getImpactBadge(tip.impact)}
                  </div>
                  <div className="text-right text-sm text-muted-foreground">
                    <div>Seen {tip.timesSeen} times</div>
                    <div>{tip.successRate}% success rate</div>
                  </div>
                </div>
                
                <p className="text-sm text-muted-foreground">{tip.tip}</p>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 text-sm">
                    <span><strong>Frequency:</strong> {tip.frequency}</span>
                    <span><strong>Suggest:</strong> {tip.suggestedPairing}</span>
                    <span><strong>Category:</strong> {tip.category}</span>
                  </div>
                  <Button variant="outline" size="sm">
                    Mark as Applied
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Service-Specific Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Service-Specific Insights</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            {serviceInsights.map((insight, index) => (
              <div key={insight.service} className="p-4 border rounded-lg space-y-2">
                <h4 className="font-medium">{insight.service}</h4>
                <div className="space-y-1 text-sm text-muted-foreground">
                  <div><strong>Frequency:</strong> {insight.frequency} times/week</div>
                  <div><strong>Avg. Value:</strong> ₵{insight.avgValue}</div>
                  <div><strong>Best Time:</strong> {insight.bestTime}</div>
                  <div><strong>Main Customers:</strong> {insight.customerType}</div>
                </div>
                <div className="pt-2 border-t">
                  <p className="text-sm font-medium text-blue-600">{insight.tip}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
