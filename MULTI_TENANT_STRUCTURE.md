# Multi-Tenant Role-Based Access Control Structure

## Overview
The application now supports a hierarchical multi-tenant structure with role-based access control (RBAC). Each user can view data within their scope and manage entities beneath them in the hierarchy.

## Role Hierarchy (Top to Bottom)
1. **Agent** (Level 5) - Highest level, can see all data across merchants
2. **Merchant** (Level 4) - Can see their own data and all their merchant admins
3. **Merchant Admin** (Level 3) - Can see their data and all their branch admins
4. **Branch Admin** (Level 2) - Can see their data and all their tellers
5. **Teller** (Level 1) - Can only see their own data

## Key Features

### Authentication System
- **Login Page**: `/login` with demo accounts for each role level
- **Role-based Authentication**: Users are authenticated based on their role and organization
- **Session Management**: User sessions are stored in localStorage (can be upgraded to secure tokens)

### Data Access Control
- **Hierarchical Data Filtering**: Users can only access data within their scope
- **Entity-based Permissions**: Each data record is associated with specific entities in the hierarchy
- **Dynamic Data Scoping**: Data is filtered based on the user's current scope and accessible entities

### UI Components

#### Authentication Components
- `components/auth/login-form.tsx` - Login form with demo accounts
- `components/auth/role-switcher.tsx` - Allows users to switch between accessible entities
- `components/layout/tenant-header.tsx` - Shows current scope and quick metrics

#### Dashboard Components
- `components/dashboard/scope-indicator.tsx` - Visual indicator of current data scope
- Updated dashboard pages to use tenant-aware data

### Data Structure

#### User Types
```typescript
interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  organizationId: string;
  organizationName: string;
  // Hierarchical relationships
  agentId?: string;
  merchantId?: string;
  merchantAdminId?: string;
  branchAdminId?: string;
  childrenIds?: string[];
}
```

#### Data Scope
```typescript
interface DataScope {
  level: UserRole;
  entityId: string;
  entityName: string;
  canViewChildren: boolean;
  accessibleEntityIds: string[];
}
```

### Demo Accounts
All demo accounts use password: "password"

1. **<EMAIL>** - Agent level access
   - Can see all merchants and their data
   - Full system administration capabilities

2. **<EMAIL>** - Merchant level access
   - Can see their merchant admins and branches
   - Limited to their merchant organization

3. **<EMAIL>** - Merchant Admin level access
   - Can see their branch admins and tellers
   - Limited to their regional scope

4. **<EMAIL>** - Branch Admin level access
   - Can see their tellers
   - Limited to their branch scope

5. **<EMAIL>** - Teller level access
   - Can only see their own data
   - Most restricted access level

## File Structure

### Core Authentication & Authorization
- `lib/auth/types.ts` - Type definitions for users, roles, and permissions
- `lib/auth/context.tsx` - React context for authentication state
- `lib/rbac/permissions.ts` - Role-based access control logic
- `lib/data/tenant-data.ts` - Multi-tenant data structure
- `lib/api/data-service.ts` - Data service with tenant filtering
- `middleware.ts` - Route protection middleware

### UI Components
- `app/(auth)/login/page.tsx` - Login page
- `components/auth/` - Authentication-related components
- `components/layout/tenant-header.tsx` - Tenant-aware header
- Updated `components/layout/TopBar.tsx` and `DashboardLayout.tsx`

## Usage

### Starting the Application
1. Run `npm run dev`
2. Navigate to `http://localhost:3000`
3. You'll be redirected to `/login`
4. Use any of the demo accounts to test different role levels

### Testing Role-Based Access
1. Login with different demo accounts
2. Notice how the data changes based on the user's role and scope
3. Use the role switcher in the top bar to switch between accessible entities
4. Observe how the tenant header shows current scope and metrics

### Data Filtering
- All customer data, alerts, and metrics are filtered based on the user's accessible entities
- Higher-level roles can see aggregated data from their subordinates
- Lower-level roles only see data within their direct scope

## Security Considerations

### Current Implementation (Demo)
- Uses localStorage for session storage
- Mock authentication with hardcoded users
- Client-side route protection

### Production Recommendations
1. **Secure Authentication**: Implement JWT tokens with proper validation
2. **Server-side Authorization**: Move permission checks to API endpoints
3. **Database Integration**: Replace mock data with real database queries
4. **Audit Logging**: Track data access and modifications
5. **Rate Limiting**: Implement API rate limiting
6. **HTTPS**: Ensure all communications are encrypted

## Extending the System

### Adding New Roles
1. Update `UserRole` type in `lib/auth/types.ts`
2. Add role to `ROLE_HIERARCHY` and `ROLE_PERMISSIONS`
3. Update permission logic in `lib/rbac/permissions.ts`
4. Add role-specific UI components if needed

### Adding New Data Types
1. Create tenant-aware data structure in `lib/data/tenant-data.ts`
2. Add filtering logic to `lib/api/data-service.ts`
3. Update dashboard components to use the new data service methods

### Customizing Permissions
1. Modify `ROLE_PERMISSIONS` in `lib/auth/types.ts`
2. Update permission checking logic in `lib/rbac/permissions.ts`
3. Add new permission types as needed

This structure provides a solid foundation for a multi-tenant analytics dashboard with proper role-based access control and can be extended as needed for specific business requirements.
