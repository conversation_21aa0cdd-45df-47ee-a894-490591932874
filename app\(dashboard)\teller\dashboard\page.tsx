"use client";

import {
  User,
  DollarSign,
  Clock,
  Activity,
  AlertTriangle
} from "lucide-react";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON>ton } from "@/components/ui/button";
import { formatCurrency } from "@/lib/utils/currency";
import Link from "next/link";

// Mock data for Teller dashboard

const customerInteractionData = [
  { service: "Mobile Top-up", frequency: 45, customerPreference: "High", suggestedPairing: "Bill Payment" },
  { service: "Bill Payment", frequency: 38, customerPreference: "High", suggestedPairing: "Account Services" },
  { service: "Money Transfer", frequency: 32, customerPreference: "Medium", suggestedPairing: "Mobile Top-up" },
  { service: "Account Services", frequency: 25, customerPreference: "Medium", suggestedPairing: "Bill Payment" },
];

const fraudAlerts = [
  { id: "FA001", type: "Duplicate Payment", customer: "John Doe", amount: 500, time: "10:30 AM", risk: "High" },
  { id: "FA002", type: "Unusual Pattern", customer: "Jane Smith", amount: 1200, time: "2:15 PM", risk: "Medium" },
  { id: "FA003", type: "Multiple Attempts", customer: "Bob Johnson", amount: 300, time: "4:45 PM", risk: "Low" },
];



export default function TellerDashboard() {
  const { user } = useAuth();

  if (!user || user.role !== 'teller') {
    return <div>Access denied. Teller role required.</div>;
  }

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Dashboard</h2>
        <p className="text-muted-foreground">
          Your personal performance and transaction records
        </p>
      </div>

      <FilterBar />

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Today's Transactions"
          value="28"
          icon={Activity}
          trend={{ value: 12.5, isPositive: true }}
        />
        <KpiCard
          title="Today's Revenue"
          value={formatCurrency(7280)}
          icon={DollarSign}
          trend={{ value: 8.7, isPositive: true }}
        />
        <KpiCard
          title="Avg. Processing Time"
          value="2.1 min"
          icon={Clock}
          trend={{ value: 15.2, isPositive: false }}
        />

      </div>

      {/* Personal Sales Summary */}
      {/* <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Personal Performance vs Branch Average">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={personalPerformanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="period" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="myTransactions" fill="#0088FE" name="My Transactions" />
              <Bar dataKey="branchAvg" fill="#00C49F" name="Branch Average" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Daily Performance Trend">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={dailyPerformanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="day" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="transactions" stroke="#0088FE" name="Transactions" />
              <Line type="monotone" dataKey="efficiency" stroke="#00C49F" name="Efficiency %" />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div> */}



      {/* Fraud Alerts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5" />
            <span>Recent Fraud Alerts</span>
            <Link href="/teller/fraud-alerts">
              <Button variant="ghost" size="sm" className="ml-auto">
                View All Alerts →
              </Button>
            </Link>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {fraudAlerts.slice(0, 2).map((alert) => (
              <div key={alert.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium">{alert.type}</div>
                  <div className="text-sm text-muted-foreground">
                    {alert.customer} • {formatCurrency(alert.amount)} • {alert.time}
                  </div>
                </div>
                <Badge
                  variant={
                    alert.risk === 'High' ? 'destructive' :
                    alert.risk === 'Medium' ? 'secondary' :
                    'outline'
                  }
                >
                  {alert.risk} Risk
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>



      {/* Customer Service Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <span>Customer Service Insights</span>
            <Link href="/teller/interaction-tips">
              <Button variant="ghost" size="sm" className="ml-auto">
                View All Tips →
              </Button>
            </Link>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {customerInteractionData.slice(0, 3).map((item) => (
              <div key={item.service} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium">{item.service}</div>
                  <div className="text-sm text-muted-foreground">
                    {item.frequency} transactions this week • Often paired with {item.suggestedPairing}
                  </div>
                </div>
                <Badge variant={item.customerPreference === 'High' ? 'default' : 'secondary'}>
                  {item.customerPreference} Preference
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
