"use client";

import {
  DollarSign,
  Clock,
  Activity,
  AlertTriangle,
  Target
} from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Legend,
  AreaChart,
  Area
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { formatCurrency } from "@/lib/utils/currency";
import Link from "next/link";

// Mock data for Teller dashboard
const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

// Daily performance data
const dailyPerformanceData = [
  { day: "Mon", transactions: 18, revenue: 4680, avgValue: 260 },
  { day: "Tue", transactions: 22, revenue: 5720, avgValue: 260 },
  { day: "Wed", transactions: 19, revenue: 4940, avgValue: 260 },
  { day: "Thu", transactions: 25, revenue: 6500, avgValue: 260 },
  { day: "Fri", transactions: 28, revenue: 7280, avgValue: 260 },
  { day: "Sat", transactions: 15, revenue: 3900, avgValue: 260 },
];

// Weekly performance trends
const weeklyTrends = [
  { week: "Week 1", transactions: 89, revenue: 23400, goals: 85 },
  { week: "Week 2", transactions: 92, revenue: 24800, goals: 85 },
  { week: "Week 3", transactions: 87, revenue: 22600, goals: 85 },
  { week: "Week 4", transactions: 95, revenue: 26200, goals: 85 },
];

// Transaction types distribution
const transactionTypes = [
  { name: "Mobile Top-up", value: 35, count: 45, revenue: 8500 },
  { name: "Bill Payment", value: 28, count: 38, revenue: 12400 },
  { name: "Money Transfer", value: 22, count: 32, revenue: 9800 },
  { name: "Account Services", value: 15, count: 25, revenue: 4200 },
];

// Hourly transaction pattern
const hourlyPattern = [
  { hour: "8am", transactions: 8 },
  { hour: "9am", transactions: 15 },
  { hour: "10am", transactions: 22 },
  { hour: "11am", transactions: 28 },
  { hour: "12pm", transactions: 35 },
  { hour: "1pm", transactions: 32 },
  { hour: "2pm", transactions: 25 },
  { hour: "3pm", transactions: 18 },
  { hour: "4pm", transactions: 12 },
  { hour: "5pm", transactions: 8 },
];

// Goal progress data
const goalProgress = [
  { metric: "Daily Transactions", achieved: 28, target: 30, percentage: 93 },
  { metric: "Daily Revenue", achieved: 7280, target: 8000, percentage: 91 },
  { metric: "Customer Satisfaction", achieved: 4.6, target: 4.5, percentage: 102 },
];

const fraudAlerts = [
  { id: "FA001", type: "Duplicate Payment", customer: "John Doe", amount: 500, time: "10:30 AM", risk: "High" },
  { id: "FA002", type: "Unusual Pattern", customer: "Jane Smith", amount: 1200, time: "2:15 PM", risk: "Medium" },
  { id: "FA003", type: "Multiple Attempts", customer: "Bob Johnson", amount: 300, time: "4:45 PM", risk: "Low" },
];



export default function TellerDashboard() {
  const { user } = useAuth();

  if (!user || user.role !== 'teller') {
    return <div>Access denied. Teller role required.</div>;
  }

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Dashboard</h2>
        <p className="text-muted-foreground">
          Your personal performance and transaction records
        </p>
      </div>

      <FilterBar />

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Today's Transactions"
          value="28"
          icon={Activity}
          trend={{ value: 12.5, isPositive: true }}
        />
        <KpiCard
          title="Today's Revenue"
          value={formatCurrency(7280)}
          icon={DollarSign}
          trend={{ value: 8.7, isPositive: true }}
        />
        <KpiCard
          title="Avg. Processing Time"
          value="2.1 min"
          icon={Clock}
          trend={{ value: 15.2, isPositive: false }}
        />

      </div>

      {/* Personal Sales Summary */}
      {/* <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Personal Performance vs Branch Average">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={personalPerformanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="period" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="myTransactions" fill="#0088FE" name="My Transactions" />
              <Bar dataKey="branchAvg" fill="#00C49F" name="Branch Average" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Daily Performance Trend">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={dailyPerformanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="day" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="transactions" stroke="#0088FE" name="Transactions" />
              <Line type="monotone" dataKey="efficiency" stroke="#00C49F" name="Efficiency %" />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div> */}







      {/* Performance Charts */}
      <div className="grid gap-6 md:grid-cols-2 max-w-full">
        {/* Daily Performance */}
        <ChartContainer title="Daily Performance This Week">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dailyPerformanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="day" />
              <YAxis />
              <Tooltip formatter={(value, name) => {
                if (name === "revenue") return [formatCurrency(Number(value)), "Revenue"];
                return [value, name];
              }} />
              <Legend />
              <Bar dataKey="transactions" fill={COLORS[0]} name="Transactions" />
              <Bar dataKey="revenue" fill={COLORS[1]} name="Revenue" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

              {/* Weekly Trends */}
        <ChartContainer title="Weekly Performance Trends">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={weeklyTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="week" />
              <YAxis />
              <Tooltip formatter={(value, name) => {
                if (name === "revenue") return [formatCurrency(Number(value)), "Revenue"];
                return [value, name];
              }} />
              <Legend />
              <Line type="monotone" dataKey="transactions" stroke={COLORS[0]} strokeWidth={3} name="Transactions" />
              <Line type="monotone" dataKey="revenue" stroke={COLORS[1]} strokeWidth={2} name="Revenue" />
              <Line type="monotone" dataKey="goals" stroke={COLORS[2]} strokeWidth={2} strokeDasharray="5 5" name="Goal" />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>

      </div>

      {/* Additional Charts */}
      <div className="grid gap-6 md:grid-cols-1 max-w-full">

        {/* Hourly Pattern */}
        <ChartContainer title="Today's Transaction Pattern">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={hourlyPattern}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="hour" />
              <YAxis />
              <Tooltip />
              <Area type="monotone" dataKey="transactions" stroke={COLORS[0]} fill={COLORS[0]} fillOpacity={0.3} name="Transactions" />
            </AreaChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

          {/* Fraud Alerts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5" />
            <span>Recent Fraud Alerts</span>
            <Link href="/teller/fraud-alerts">
              <Button variant="ghost" size="sm" className="ml-auto">
                View All Alerts →
              </Button>
            </Link>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {fraudAlerts.slice(0, 2).map((alert) => (
              <div key={alert.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium">{alert.type}</div>
                  <div className="text-sm text-muted-foreground">
                    {alert.customer} • {formatCurrency(alert.amount)} • {alert.time}
                  </div>
                </div>
                <Badge
                  variant={
                    alert.risk === 'High' ? 'destructive' :
                    alert.risk === 'Medium' ? 'secondary' :
                    'outline'
                  }
                >
                  {alert.risk} Risk
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      
    </div>
  );
}
