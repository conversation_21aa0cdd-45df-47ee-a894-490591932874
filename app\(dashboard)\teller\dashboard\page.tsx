"use client";

import {
  User,
  TrendingUp,
  DollarSign,
  Clock,
  AlertTriangle,
  Target,
  Activity,
  Award,
  BarChart3,
  Users
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Legend,
  AreaChart,
  Area
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { TellerNav } from "@/components/teller/teller-nav";
import { formatCurrency } from "@/lib/utils/currency";
import Link from "next/link";

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

// Mock data for Teller dashboard
const personalPerformanceData = [
  { period: "Week 1", myTransactions: 89, branchAvg: 76, myValue: 278, branchAvgValue: 245 },
  { period: "Week 2", myTransactions: 92, branchAvg: 78, myValue: 289, branchAvgValue: 250 },
  { period: "Week 3", myTransactions: 87, branchAvg: 82, myValue: 267, branchAvgValue: 255 },
  { period: "Week 4", myTransactions: 95, branchAvg: 85, myValue: 312, branchAvgValue: 260 },
];

const dailyPerformanceData = [
  { day: "Monday", transactions: 18, value: 4680, efficiency: 92 },
  { day: "Tuesday", transactions: 22, value: 5720, efficiency: 88 },
  { day: "Wednesday", transactions: 19, value: 4940, efficiency: 90 },
  { day: "Thursday", transactions: 25, value: 6500, efficiency: 95 },
  { day: "Friday", transactions: 28, value: 7280, efficiency: 93 },
];

const customerInteractionData = [
  { service: "Mobile Top-up", frequency: 45, customerPreference: "High", suggestedPairing: "Bill Payment" },
  { service: "Bill Payment", frequency: 38, customerPreference: "High", suggestedPairing: "Account Services" },
  { service: "Money Transfer", frequency: 32, customerPreference: "Medium", suggestedPairing: "Mobile Top-up" },
  { service: "Account Services", frequency: 25, customerPreference: "Medium", suggestedPairing: "Bill Payment" },
];

const fraudAlerts = [
  { id: "FA001", type: "Duplicate Payment", customer: "John Doe", amount: 500, time: "10:30 AM", risk: "High" },
  { id: "FA002", type: "Unusual Pattern", customer: "Jane Smith", amount: 1200, time: "2:15 PM", risk: "Medium" },
  { id: "FA003", type: "Multiple Attempts", customer: "Bob Johnson", amount: 300, time: "4:45 PM", risk: "Low" },
];

const weeklyGoals = [
  { metric: "Transactions", target: 100, achieved: 95, percentage: 95 },
  { metric: "Revenue", target: 25000, achieved: 27500, percentage: 110 },
  { metric: "Customer Satisfaction", target: 90, achieved: 92, percentage: 102 },
  { metric: "Processing Speed", target: 180, achieved: 165, percentage: 92 },
];

const customerTips = [
  {
    pattern: "Mobile Top-up + Bill Payment",
    frequency: "78%",
    tip: "Customers who buy mobile top-up often need bill payment services",
    action: "Suggest bill payment after mobile top-up"
  },
  {
    pattern: "Account Opening + Money Transfer",
    frequency: "65%",
    tip: "New account holders frequently need money transfer services",
    action: "Offer money transfer setup during account opening"
  },
  {
    pattern: "Friday Afternoon Rush",
    frequency: "Peak",
    tip: "Friday 2-4 PM sees highest traffic for salary-related services",
    action: "Prepare for increased money transfer and withdrawal requests"
  }
];

const transactionTypeData = [
  { name: "Mobile Top-up", value: 35, count: 45 },
  { name: "Bill Payment", value: 28, count: 38 },
  { name: "Money Transfer", value: 22, count: 32 },
  { name: "Account Services", value: 15, count: 25 },
];

export default function TellerDashboard() {
  const { user, currentScope } = useAuth();

  if (!user || user.role !== 'teller') {
    return <div>Access denied. Teller role required.</div>;
  }

  return (
    <div className="w-full space-y-8">
      {/* <TellerNav /> */}

      <div className="mb-6">
        <h2 className="text-2xl font-bold">Teller Dashboard</h2>
        <p className="text-muted-foreground">
          Your personal performance and transaction records
        </p>
      </div>

      <FilterBar />

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Today's Transactions"
          value="28"
          icon={Activity}
          trend={{ value: 12.5, isPositive: true }}
        />
        <KpiCard
          title="Today's Revenue"
          value={formatCurrency(7280)}
          icon={DollarSign}
          trend={{ value: 8.7, isPositive: true }}
        />
        <KpiCard
          title="Avg. Processing Time"
          value="2.1 min"
          icon={Clock}
          trend={{ value: 15.2, isPositive: false }}
        />
   
      </div>

      {/* Personal Sales Summary */}
      {/* <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Personal Performance vs Branch Average">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={personalPerformanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="period" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="myTransactions" fill="#0088FE" name="My Transactions" />
              <Bar dataKey="branchAvg" fill="#00C49F" name="Branch Average" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Daily Performance Trend">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={dailyPerformanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="day" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="transactions" stroke="#0088FE" name="Transactions" />
              <Line type="monotone" dataKey="efficiency" stroke="#00C49F" name="Efficiency %" />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div> */}



      {/* Fraud Alerts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5" />
            <span>Recent Fraud Alerts</span>
            <Link href="/teller/fraud-alerts">
              <Button variant="ghost" size="sm" className="ml-auto">
                View All Alerts →
              </Button>
            </Link>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {fraudAlerts.slice(0, 2).map((alert) => (
              <div key={alert.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium">{alert.type}</div>
                  <div className="text-sm text-muted-foreground">
                    {alert.customer} • {formatCurrency(alert.amount)} • {alert.time}
                  </div>
                </div>
                <Badge
                  variant={
                    alert.risk === 'High' ? 'destructive' :
                    alert.risk === 'Medium' ? 'secondary' :
                    'outline'
                  }
                >
                  {alert.risk} Risk
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>



      {/* Customer Service Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <span>Customer Service Insights</span>
            <Link href="/teller/interaction-tips">
              <Button variant="ghost" size="sm" className="ml-auto">
                View All Tips →
              </Button>
            </Link>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {customerInteractionData.slice(0, 3).map((item, index) => (
              <div key={item.service} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium">{item.service}</div>
                  <div className="text-sm text-muted-foreground">
                    {item.frequency} transactions this week • Often paired with {item.suggestedPairing}
                  </div>
                </div>
                <Badge variant={item.customerPreference === 'High' ? 'default' : 'secondary'}>
                  {item.customerPreference} Preference
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
