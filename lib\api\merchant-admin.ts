import { ApiClient, withRetry } from './client';
import { FallbackService } from './fallback-service';
import {
  MerchantAdminDashboard,
  CustomerAnalytics,
  BranchAnalytics,
  HighValueCustomer,
  BranchPerformance,
  TellerPerformance,
  DashboardRequest,
  CustomerRequest,
  BranchRequest,
  ApiResponse,
  PaginatedResponse,
  CustomerSegmentAnalysis,
  SpendingTrendData,
} from './types';

// Configuration for fallback behavior
const USE_FALLBACK = process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_USE_FALLBACK === 'true';

// Merchant Admin API Service
export class MerchantAdminApiService {
  private static readonly BASE_PATH = '/api/merchant-admin';

  // Dashboard Data
  static async getDashboardData(params?: DashboardRequest): Promise<MerchantAdminDashboard> {
    if (USE_FALLBACK) {
      console.log('🔄 Using fallback data for dashboard');
      return Promise.resolve(FallbackService.getMockDashboardData());
    }

    try {
      return await withRetry(() =>
        ApiClient.get<ApiResponse<MerchantAdminDashboard>>(`${this.BASE_PATH}/dashboard`, {
          params,
        }).then(response => response.data)
      );
    } catch (error) {
      console.warn('⚠️ API failed, falling back to mock data:', error);
      return FallbackService.getMockDashboardData();
    }
  }

  // High-Value Customers
  static async getHighValueCustomers(params?: CustomerRequest): Promise<PaginatedResponse<HighValueCustomer>> {
    if (USE_FALLBACK) {
      console.log('🔄 Using fallback data for high-value customers');
      const mockCustomers = FallbackService.getMockHighValueCustomers();
      return Promise.resolve(FallbackService.createPaginatedResponse(mockCustomers, params?.page, params?.limit));
    }

    try {
      return await withRetry(() =>
        ApiClient.get<PaginatedResponse<HighValueCustomer>>(`${this.BASE_PATH}/customers/high-value`, {
          params,
        })
      );
    } catch (error) {
      console.warn('⚠️ API failed, falling back to mock data:', error);
      const mockCustomers = FallbackService.getMockHighValueCustomers();
      return FallbackService.createPaginatedResponse(mockCustomers, params?.page, params?.limit);
    }
  }

  static async getCustomerAnalytics(params?: CustomerRequest): Promise<CustomerAnalytics> {
    if (USE_FALLBACK) {
      console.log('🔄 Using fallback data for customer analytics');
      return Promise.resolve(FallbackService.getMockCustomerAnalytics());
    }

    try {
      return await withRetry(() =>
        ApiClient.get<ApiResponse<CustomerAnalytics>>(`${this.BASE_PATH}/customers/analytics`, {
          params,
        }).then(response => response.data)
      );
    } catch (error) {
      console.warn('⚠️ API failed, falling back to mock data:', error);
      return FallbackService.getMockCustomerAnalytics();
    }
  }

  static async getCustomerById(customerId: string): Promise<HighValueCustomer> {
    return withRetry(() =>
      ApiClient.get<ApiResponse<HighValueCustomer>>(`${this.BASE_PATH}/customers/${customerId}`)
        .then(response => response.data)
    );
  }

  // Branch Performance
  static async getBranchPerformance(params?: BranchRequest): Promise<BranchAnalytics> {
    return withRetry(() =>
      ApiClient.get<ApiResponse<BranchAnalytics>>(`${this.BASE_PATH}/branches/performance`, {
        params,
      }).then(response => response.data)
    );
  }

  static async getBranchById(branchId: string): Promise<BranchPerformance> {
    return withRetry(() =>
      ApiClient.get<ApiResponse<BranchPerformance>>(`${this.BASE_PATH}/branches/${branchId}`)
        .then(response => response.data)
    );
  }

  static async getBranchTellers(branchId: string): Promise<TellerPerformance[]> {
    return withRetry(() =>
      ApiClient.get<ApiResponse<TellerPerformance[]>>(`${this.BASE_PATH}/branches/${branchId}/tellers`)
        .then(response => response.data)
    );
  }

  // Customer Segmentation
  static async getCustomerSegmentation(params?: DashboardRequest): Promise<CustomerSegmentAnalysis[]> {
    return withRetry(() =>
      ApiClient.get<ApiResponse<CustomerSegmentAnalysis[]>>(`${this.BASE_PATH}/customers/segmentation`, {
        params,
      }).then(response => response.data)
    );
  }

  static async getSpendingTrends(params?: DashboardRequest): Promise<SpendingTrendData[]> {
    return withRetry(() =>
      ApiClient.get<ApiResponse<SpendingTrendData[]>>(`${this.BASE_PATH}/customers/spending-trends`, {
        params,
      }).then(response => response.data)
    );
  }

  // Time Between Transactions
  static async getTimeBetweenTransactions(params?: DashboardRequest) {
    return withRetry(() =>
      ApiClient.get<ApiResponse<any>>(`${this.BASE_PATH}/analytics/time-between-transactions`, {
        params,
      }).then(response => response.data)
    );
  }

  // Transaction Analytics
  static async getTransactionAnalytics(params?: DashboardRequest) {
    return withRetry(() =>
      ApiClient.get<ApiResponse<any>>(`${this.BASE_PATH}/analytics/transactions`, {
        params,
      }).then(response => response.data)
    );
  }

  // Revenue Analytics
  static async getRevenueAnalytics(params?: DashboardRequest) {
    return withRetry(() =>
      ApiClient.get<ApiResponse<any>>(`${this.BASE_PATH}/analytics/revenue`, {
        params,
      }).then(response => response.data)
    );
  }

  // KPI Data
  static async getKPIData(params?: DashboardRequest) {
    return withRetry(() =>
      ApiClient.get<ApiResponse<any>>(`${this.BASE_PATH}/kpis`, {
        params,
      }).then(response => response.data)
    );
  }

  // Export Data
  static async exportCustomerData(params?: CustomerRequest): Promise<Blob> {
    return withRetry(() =>
      ApiClient.get(`${this.BASE_PATH}/export/customers`, {
        params,
        responseType: 'blob',
      })
    );
  }

  static async exportBranchData(params?: BranchRequest): Promise<Blob> {
    return withRetry(() =>
      ApiClient.get(`${this.BASE_PATH}/export/branches`, {
        params,
        responseType: 'blob',
      })
    );
  }
}

// Utility functions for data transformation
export const transformCustomerData = (apiCustomer: any): HighValueCustomer => {
  return {
    id: apiCustomer.id || apiCustomer.customer_id,
    name: apiCustomer.name || `${apiCustomer.first_name} ${apiCustomer.last_name}`,
    email: apiCustomer.email,
    phone: apiCustomer.phone,
    totalSpend: apiCustomer.total_spend || apiCustomer.totalSpend,
    transactions: apiCustomer.transaction_count || apiCustomer.transactions,
    lastVisit: apiCustomer.last_visit || apiCustomer.lastVisit,
    branchId: apiCustomer.branch_id || apiCustomer.branchId,
    branchName: apiCustomer.branch_name || apiCustomer.branchName,
    tellerId: apiCustomer.teller_id || apiCustomer.tellerId,
    tellerName: apiCustomer.teller_name || apiCustomer.tellerName,
    avgTransactionValue: apiCustomer.avg_transaction_value || apiCustomer.avgTransactionValue,
    customerSince: apiCustomer.customer_since || apiCustomer.customerSince,
    status: apiCustomer.status,
    segment: apiCustomer.segment,
  };
};

export const transformBranchData = (apiBranch: any): BranchPerformance => {
  return {
    id: apiBranch.id || apiBranch.branch_id,
    name: apiBranch.name || apiBranch.branch_name,
    transactions: apiBranch.transaction_count || apiBranch.transactions,
    revenue: apiBranch.total_revenue || apiBranch.revenue,
    score: apiBranch.performance_score || apiBranch.score,
    efficiency: apiBranch.efficiency_score || apiBranch.efficiency,
    tellerCount: apiBranch.teller_count || apiBranch.tellerCount,
    customerCount: apiBranch.customer_count || apiBranch.customerCount,
  };
};

// Default export
export default MerchantAdminApiService;
