import { User, DataScope, UserRole, ROLE_HIERARCHY, ROLE_PERMISSIONS } from '../auth/types';

export function calculateDataScope(user: User): DataScope {
  const accessibleEntityIds = getAccessibleEntityIds(user, user.organizationId);
  
  return {
    level: user.role,
    entityId: user.organizationId,
    entityName: user.organizationName,
    canViewChildren: user.role !== 'teller',
    accessibleEntityIds,
  };
}

export function getAccessibleEntityIds(user: User, currentEntityId?: string): string[] {
  const entityId = currentEntityId || user.organizationId;
  const accessibleIds = [entityId];
  
  // Add children IDs if user can view children
  if (user.childrenIds && user.role !== 'teller') {
    accessibleIds.push(...user.childrenIds);
    
    // For higher-level roles, recursively add grandchildren
    if (user.role === 'agent' || user.role === 'merchant') {
      // This would need to be expanded with actual hierarchy data
      // For now, we'll add mock nested IDs
      user.childrenIds.forEach(childId => {
        accessibleIds.push(...getMockChildrenIds(childId));
      });
    }
  }
  
  return accessibleIds;
}

function getMockChildrenIds(parentId: string): string[] {
  // Mock implementation - in real app, this would query the database
  const mockHierarchy: Record<string, string[]> = {
    'merchant-1': ['merchant-admin-1', 'merchant-admin-2'],
    'merchant-2': ['merchant-admin-3', 'merchant-admin-4'],
    'merchant-admin-1': ['branch-admin-1', 'branch-admin-2'],
    'merchant-admin-2': ['branch-admin-3', 'branch-admin-4'],
    'branch-admin-1': ['teller-1', 'teller-2'],
    'branch-admin-2': ['teller-3', 'teller-4'],
  };
  
  return mockHierarchy[parentId] || [];
}

export function hasPermission(
  user: User,
  resource: string,
  action: 'read' | 'write' | 'delete' | 'admin',
  targetEntityId?: string
): boolean {
  const userPermissions = ROLE_PERMISSIONS[user.role];
  
  const permission = userPermissions.find(p => 
    p.resource === resource && p.action === action
  );
  
  if (!permission) return false;
  
  // Check scope
  switch (permission.scope) {
    case 'all':
      return true;
    case 'own':
      return !targetEntityId || targetEntityId === user.organizationId;
    case 'children':
      if (!targetEntityId) return true;
      const accessibleIds = getAccessibleEntityIds(user);
      return accessibleIds.includes(targetEntityId);
    default:
      return false;
  }
}

export function canAccessEntity(user: User, entityId: string): boolean {
  const accessibleIds = getAccessibleEntityIds(user);
  return accessibleIds.includes(entityId);
}

export function filterDataByScope<T extends { entityId?: string; organizationId?: string }>(
  data: T[],
  user: User,
  currentScope?: DataScope
): T[] {
  const scope = currentScope || calculateDataScope(user);
  
  return data.filter(item => {
    const itemEntityId = item.entityId || item.organizationId;
    if (!itemEntityId) return true; // Allow items without entity association
    
    return scope.accessibleEntityIds.includes(itemEntityId);
  });
}

export function getRoleDisplayName(role: UserRole): string {
  const displayNames: Record<UserRole, string> = {
    agent: 'Agent',
    merchant: 'Merchant',
    merchant_admin: 'Merchant Admin',
    branch_admin: 'Branch Admin',
    teller: 'Teller',
  };
  
  return displayNames[role];
}

export function canSwitchToRole(currentRole: UserRole, targetRole: UserRole): boolean {
  return ROLE_HIERARCHY[currentRole] >= ROLE_HIERARCHY[targetRole];
}

export function getAvailableSwitchableEntities(user: User): Array<{
  id: string;
  name: string;
  type: UserRole;
}> {
  const entities: Array<{ id: string; name: string; type: UserRole }> = [];
  
  // Add current entity
  entities.push({
    id: user.organizationId,
    name: user.organizationName,
    type: user.role,
  });
  
  // Add children entities if user can view them
  if (user.childrenIds && user.role !== 'teller') {
    user.childrenIds.forEach(childId => {
      // Mock entity names - in real app, fetch from database
      const childEntity = getMockEntityInfo(childId);
      if (childEntity) {
        entities.push(childEntity);
      }
    });
  }
  
  return entities;
}

function getMockEntityInfo(entityId: string): { id: string; name: string; type: UserRole } | null {
  // Mock implementation - replace with real data fetching
  const mockEntities: Record<string, { name: string; type: UserRole }> = {
    'merchant-1': { name: 'TechCorp Payments', type: 'merchant' },
    'merchant-2': { name: 'RetailPay Solutions', type: 'merchant' },
    'merchant-admin-1': { name: 'TechCorp East Region', type: 'merchant_admin' },
    'merchant-admin-2': { name: 'TechCorp West Region', type: 'merchant_admin' },
    'branch-admin-1': { name: 'Downtown Branch', type: 'branch_admin' },
    'branch-admin-2': { name: 'Uptown Branch', type: 'branch_admin' },
    'teller-1': { name: 'Teller Station 1', type: 'teller' },
    'teller-2': { name: 'Teller Station 2', type: 'teller' },
  };
  
  const entity = mockEntities[entityId];
  if (!entity) return null;
  
  return {
    id: entityId,
    name: entity.name,
    type: entity.type,
  };
}
