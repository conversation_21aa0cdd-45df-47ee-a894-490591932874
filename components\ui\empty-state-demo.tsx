"use client";

import { DataTable, TableColumn } from "./data-table";

interface DemoData {
  id: string;
  name: string;
  value: number;
}

export function EmptyStateDemo() {
  // Empty data array to demonstrate empty state
  const emptyData: DemoData[] = [];

  const columns: TableColumn<DemoData>[] = [
    {
      key: "name",
      title: "Name",
      minWidth: "150px"
    },
    {
      key: "value",
      title: "Value",
      minWidth: "100px"
    }
  ];

  return (
    <div className="p-6">
      <h2 className="text-xl font-bold mb-4">Empty State Demo</h2>
      <DataTable
        title="Demo Table"
        data={emptyData}
        columns={columns}
        emptyTitle="No demo data available"
        emptyMessage="This is a demonstration of the empty state. Try adding some data to see the table in action."
        searchPlaceholder="Search demo data..."
      />
    </div>
  );
}
