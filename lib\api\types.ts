// Base API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: 'success' | 'error';
  timestamp?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Date Range Filter
export interface DateRangeFilter {
  startDate: string;
  endDate: string;
}

// Merchant Admin API Types
export interface MerchantAdminDashboard {
  kpis: {
    totalBranches: number;
    activeTellers: number;
    totalTransactions: number;
    totalRevenue: number;
    trends: {
      branches: number;
      tellers: number;
      transactions: number;
      revenue: number;
    };
  };
  branchPerformance: BranchPerformance[];
  tellerPerformance: TellerPerformance[];
  customerBehavior: CustomerBehaviorData[];
  peakTransactionPeriods: PeakTransactionData[];
  customerSegmentation: CustomerSegmentData[];
  topProducts: ProductPerformanceData[];
  highValueCustomers: HighValueCustomer[];
}

export interface BranchPerformance {
  id: string;
  name: string;
  transactions: number;
  revenue: number;
  score: number;
  efficiency: number;
  tellerCount: number;
  customerCount: number;
}

export interface TellerPerformance {
  id: string;
  name: string;
  branchId: string;
  branchName: string;
  transactions: number;
  avgValue: number;
  score: number;
  efficiency: number;
  customersSaved: number;
}

export interface CustomerBehaviorData {
  hour: string;
  newCustomers: number;
  returningCustomers: number;
  avgTransaction: number;
  totalTransactions: number;
}

export interface PeakTransactionData {
  day: string;
  morning: number;
  afternoon: number;
  evening: number;
  total: number;
}

export interface CustomerSegmentData {
  name: string;
  value: number;
  count: number;
  avgSpend: number;
  percentage: number;
}

export interface ProductPerformanceData {
  id: string;
  product: string;
  sales: number;
  revenue: number;
  growth: number;
  category: string;
}

export interface HighValueCustomer {
  id: string;
  name: string;
  email: string;
  phone?: string;
  totalSpend: number;
  transactions: number;
  lastVisit: string;
  branchId: string;
  branchName: string;
  tellerId?: string;
  tellerName?: string;
  avgTransactionValue: number;
  customerSince: string;
  status: 'active' | 'inactive' | 'at_risk';
  segment: string;
}

// Customer Analytics Types
export interface CustomerAnalytics {
  customers: HighValueCustomer[];
  metrics: {
    totalCustomers: number;
    highValueCount: number;
    averageSpend: number;
    retentionRate: number;
    growthRate: number;
  };
  segmentAnalysis: CustomerSegmentAnalysis[];
  spendingTrends: SpendingTrendData[];
}

export interface CustomerSegmentAnalysis {
  segment: string;
  count: number;
  avgSpend: number;
  avgTransactionValue: number;
  purchaseFrequency: number;
  retentionRate: number;
  growthRate: number;
}

export interface SpendingTrendData {
  month: string;
  totalSpend: number;
  avgSpend: number;
  customerCount: number;
  transactionCount: number;
}

// Branch Performance Types
export interface BranchAnalytics {
  branches: BranchPerformance[];
  comparison: BranchComparisonData[];
  rankings: BranchRankingData[];
  trends: BranchTrendData[];
}

export interface BranchComparisonData {
  branchId: string;
  branchName: string;
  currentPeriod: {
    transactions: number;
    revenue: number;
    customers: number;
  };
  previousPeriod: {
    transactions: number;
    revenue: number;
    customers: number;
  };
  growth: {
    transactions: number;
    revenue: number;
    customers: number;
  };
}

export interface BranchRankingData {
  rank: number;
  branchId: string;
  branchName: string;
  score: number;
  category: 'revenue' | 'transactions' | 'efficiency' | 'customer_satisfaction';
}

export interface BranchTrendData {
  branchId: string;
  branchName: string;
  data: Array<{
    date: string;
    transactions: number;
    revenue: number;
    customers: number;
  }>;
}

// API Request Types
export interface DashboardRequest {
  dateRange?: DateRangeFilter;
  branchIds?: string[];
  tellerIds?: string[];
  includeInactive?: boolean;
}

export interface CustomerRequest extends DashboardRequest {
  segment?: string;
  minSpend?: number;
  maxSpend?: number;
  sortBy?: 'totalSpend' | 'transactions' | 'lastVisit' | 'customerSince';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface BranchRequest extends DashboardRequest {
  includeMetrics?: boolean;
  includeTrends?: boolean;
  includeComparison?: boolean;
}

// Error Types
export interface ApiError {
  message: string;
  code?: string;
  details?: any;
  timestamp?: string;
}
