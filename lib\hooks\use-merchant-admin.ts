import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { MerchantAdminApiService } from '../api/merchant-admin';
import {
  DashboardRequest,
  CustomerRequest,
  BranchRequest,
  MerchantAdminDashboard,
  CustomerAnalytics,
  BranchAnalytics,
  HighValueCustomer,
} from '../api/types';

// Query Keys
export const merchantAdminKeys = {
  all: ['merchant-admin'] as const,
  dashboard: (params?: DashboardRequest) => [...merchantAdminKeys.all, 'dashboard', params] as const,
  customers: () => [...merchantAdminKeys.all, 'customers'] as const,
  highValueCustomers: (params?: CustomerRequest) => [...merchantAdminKeys.customers(), 'high-value', params] as const,
  customerAnalytics: (params?: CustomerRequest) => [...merchantAdminKeys.customers(), 'analytics', params] as const,
  customer: (id: string) => [...merchantAdminKeys.customers(), id] as const,
  branches: () => [...merchantAdminKeys.all, 'branches'] as const,
  branchPerformance: (params?: BranchRequest) => [...merchantAdminKeys.branches(), 'performance', params] as const,
  branch: (id: string) => [...merchantAdminKeys.branches(), id] as const,
  branchTellers: (branchId: string) => [...merchantAdminKeys.branches(), branchId, 'tellers'] as const,
  segmentation: (params?: DashboardRequest) => [...merchantAdminKeys.all, 'segmentation', params] as const,
  spendingTrends: (params?: DashboardRequest) => [...merchantAdminKeys.all, 'spending-trends', params] as const,
  kpis: (params?: DashboardRequest) => [...merchantAdminKeys.all, 'kpis', params] as const,
};

// Dashboard Hook
export const useMerchantAdminDashboard = (params?: DashboardRequest) => {
  return useQuery({
    queryKey: merchantAdminKeys.dashboard(params),
    queryFn: () => MerchantAdminApiService.getDashboardData(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

// High-Value Customers Hook
export const useHighValueCustomers = (params?: CustomerRequest) => {
  return useQuery({
    queryKey: merchantAdminKeys.highValueCustomers(params),
    queryFn: () => MerchantAdminApiService.getHighValueCustomers(params),
    staleTime: 3 * 60 * 1000, // 3 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

// Customer Analytics Hook
export const useCustomerAnalytics = (params?: CustomerRequest) => {
  return useQuery({
    queryKey: merchantAdminKeys.customerAnalytics(params),
    queryFn: () => MerchantAdminApiService.getCustomerAnalytics(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

// Single Customer Hook
export const useCustomer = (customerId: string) => {
  return useQuery({
    queryKey: merchantAdminKeys.customer(customerId),
    queryFn: () => MerchantAdminApiService.getCustomerById(customerId),
    enabled: !!customerId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
};

// Branch Performance Hook
export const useBranchPerformance = (params?: BranchRequest) => {
  return useQuery({
    queryKey: merchantAdminKeys.branchPerformance(params),
    queryFn: () => MerchantAdminApiService.getBranchPerformance(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

// Single Branch Hook
export const useBranch = (branchId: string) => {
  return useQuery({
    queryKey: merchantAdminKeys.branch(branchId),
    queryFn: () => MerchantAdminApiService.getBranchById(branchId),
    enabled: !!branchId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
};

// Branch Tellers Hook
export const useBranchTellers = (branchId: string) => {
  return useQuery({
    queryKey: merchantAdminKeys.branchTellers(branchId),
    queryFn: () => MerchantAdminApiService.getBranchTellers(branchId),
    enabled: !!branchId,
    staleTime: 3 * 60 * 1000, // 3 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
};

// Customer Segmentation Hook
export const useCustomerSegmentation = (params?: DashboardRequest) => {
  return useQuery({
    queryKey: merchantAdminKeys.segmentation(params),
    queryFn: () => MerchantAdminApiService.getCustomerSegmentation(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

// Spending Trends Hook
export const useSpendingTrends = (params?: DashboardRequest) => {
  return useQuery({
    queryKey: merchantAdminKeys.spendingTrends(params),
    queryFn: () => MerchantAdminApiService.getSpendingTrends(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

// KPI Data Hook
export const useMerchantAdminKPIs = (params?: DashboardRequest) => {
  return useQuery({
    queryKey: merchantAdminKeys.kpis(params),
    queryFn: () => MerchantAdminApiService.getKPIData(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

// Time Between Transactions Hook
export const useTimeBetweenTransactions = (params?: DashboardRequest) => {
  return useQuery({
    queryKey: [...merchantAdminKeys.all, 'time-between-transactions', params],
    queryFn: () => MerchantAdminApiService.getTimeBetweenTransactions(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

// Transaction Analytics Hook
export const useTransactionAnalytics = (params?: DashboardRequest) => {
  return useQuery({
    queryKey: [...merchantAdminKeys.all, 'transaction-analytics', params],
    queryFn: () => MerchantAdminApiService.getTransactionAnalytics(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

// Revenue Analytics Hook
export const useRevenueAnalytics = (params?: DashboardRequest) => {
  return useQuery({
    queryKey: [...merchantAdminKeys.all, 'revenue-analytics', params],
    queryFn: () => MerchantAdminApiService.getRevenueAnalytics(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

// Utility hook for invalidating all merchant admin queries
export const useInvalidateMerchantAdminQueries = () => {
  const queryClient = useQueryClient();
  
  return {
    invalidateAll: () => queryClient.invalidateQueries({ queryKey: merchantAdminKeys.all }),
    invalidateDashboard: () => queryClient.invalidateQueries({ queryKey: merchantAdminKeys.dashboard() }),
    invalidateCustomers: () => queryClient.invalidateQueries({ queryKey: merchantAdminKeys.customers() }),
    invalidateBranches: () => queryClient.invalidateQueries({ queryKey: merchantAdminKeys.branches() }),
  };
};
