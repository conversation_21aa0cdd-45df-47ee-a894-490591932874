"use client";

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { User, AuthState, DataScope, UserRole, AuthError } from './types';
import { calculateDataScope, getAccessibleEntityIds } from '../rbac/permissions';

interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<{ redirectTo: string }>;
  logout: () => void;
  switchScope: (entityId: string, entityName: string) => void;
  hasPermission: (resource: string, action: string) => boolean;
}

const AuthContext = createContext<AuthContextType | null>(null);

type AuthAction =
  | { type: 'LOGIN_START' }
  | { type: 'LOGIN_SUCCESS'; payload: User }
  | { type: 'LOGIN_ERROR'; payload?: AuthError }
  | { type: 'LOGOUT' }
  | { type: 'SWITCH_SCOPE'; payload: DataScope }
  | { type: 'CLEAR_ERROR' }
  | { type: 'SESSION_EXPIRED' }
  | { type: 'SESSION_RESTORED'; payload: User }
  | { type: 'SESSION_CHECK_COMPLETE' };

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_START':
      return { ...state, isLoading: true, error: null };
    case 'LOGIN_SUCCESS':
      const user = action.payload;
      const scope = calculateDataScope(user);
      const sessionExpiresAt = Date.now() + (7 * 24 * 60 * 60 * 1000); // 7 days
      return {
        user,
        isAuthenticated: true,
        isLoading: false,
        currentScope: scope,
        error: null,
        sessionExpiresAt,
      };
    case 'LOGIN_ERROR':
      return {
        user: null,
        isAuthenticated: false,
        isLoading: false,
        currentScope: {
          level: 'teller',
          entityId: '',
          entityName: '',
          canViewChildren: false,
          accessibleEntityIds: [],
        },
        error: action.payload || { code: 'LOGIN_FAILED', message: 'Login failed' },
        sessionExpiresAt: null,
      };
    case 'LOGOUT':
      return {
        user: null,
        isAuthenticated: false,
        isLoading: false,
        currentScope: {
          level: 'teller',
          entityId: '',
          entityName: '',
          canViewChildren: false,
          accessibleEntityIds: [],
        },
        error: null,
        sessionExpiresAt: null,
      };
    case 'SWITCH_SCOPE':
      return { ...state, currentScope: action.payload };
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    case 'SESSION_EXPIRED':
      return {
        user: null,
        isAuthenticated: false,
        isLoading: false,
        currentScope: {
          level: 'teller',
          entityId: '',
          entityName: '',
          canViewChildren: false,
          accessibleEntityIds: [],
        },
        error: { code: 'SESSION_EXPIRED', message: 'Session has expired' },
        sessionExpiresAt: null,
      };
    case 'SESSION_RESTORED':
      const restoredUser = action.payload;
      const restoredScope = calculateDataScope(restoredUser);
      return {
        user: restoredUser,
        isAuthenticated: true,
        isLoading: false,
        currentScope: restoredScope,
        error: null,
        sessionExpiresAt: Date.now() + (7 * 24 * 60 * 60 * 1000), // 7 days
      };
    case 'SESSION_CHECK_COMPLETE':
      return {
        ...state,
        isLoading: false,
      };
    default:
      return state;
  }
};

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: true, // Start with loading true to check for existing session
  currentScope: {
    level: 'teller',
    entityId: '',
    entityName: '',
    canViewChildren: false,
    accessibleEntityIds: [],
  },
  error: null,
  sessionExpiresAt: null,
};

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check for existing session on mount
  useEffect(() => {
    const checkExistingSession = async () => {
      try {
        const storedUser = localStorage.getItem('auth_user');
        const authCookie = document.cookie
          .split('; ')
          .find(row => row.startsWith('auth_token='));

        if (storedUser && authCookie) {
          const user = JSON.parse(storedUser);

          // Validate session hasn't expired
          const sessionData = localStorage.getItem('auth_session_expires');
          if (sessionData) {
            const expiresAt = parseInt(sessionData);
            if (Date.now() > expiresAt) {
              // Session expired, clear everything
              localStorage.removeItem('auth_user');
              localStorage.removeItem('auth_session_expires');
              document.cookie = 'auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';
              dispatch({ type: 'SESSION_EXPIRED' });
              return;
            }
          }

          // Restore valid session
          dispatch({ type: 'SESSION_RESTORED', payload: user });
        } else {
          // No valid session found
          dispatch({ type: 'SESSION_CHECK_COMPLETE' });
        }
      } catch (error) {
        console.error('Session restoration failed:', error);
        // Clear corrupted session data
        localStorage.removeItem('auth_user');
        localStorage.removeItem('auth_session_expires');
        document.cookie = 'auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';
        dispatch({ type: 'SESSION_CHECK_COMPLETE' });
      }
    };

    checkExistingSession();
  }, []);

  const login = async (email: string, password: string) => {
    dispatch({ type: 'LOGIN_START' });

    try {
      // Mock authentication - replace with real API call
      const mockUser = await mockAuthenticate(email, password);

      // Store user data and session expiration
      const sessionExpiresAt = Date.now() + (7 * 24 * 60 * 60 * 1000); // 7 days
      localStorage.setItem('auth_user', JSON.stringify(mockUser));
      localStorage.setItem('auth_session_expires', sessionExpiresAt.toString());

      // Set auth cookie for middleware
      document.cookie = `auth_token=authenticated; path=/; max-age=${60 * 60 * 24 * 7}`; // 7 days

      dispatch({ type: 'LOGIN_SUCCESS', payload: mockUser });

      // Return role-specific redirect URL
      const redirectTo = getRoleSpecificDashboard(mockUser.role);

      return { redirectTo };
    } catch (error) {
      dispatch({ type: 'LOGIN_ERROR' });
      throw error;
    }
  };

  const logout = () => {
    localStorage.removeItem('auth_user');
    localStorage.removeItem('auth_session_expires');
    // Clear the auth cookie
    document.cookie = 'auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';
    dispatch({ type: 'LOGOUT' });
  };

  const switchScope = (entityId: string, entityName: string) => {
    if (!state.user) return;

    const newScope: DataScope = {
      level: state.user.role,
      entityId,
      entityName,
      canViewChildren: state.user.role !== 'teller',
      accessibleEntityIds: getAccessibleEntityIds(state.user, entityId),
    };

    dispatch({ type: 'SWITCH_SCOPE', payload: newScope });
  };

  const hasPermission = (resource: string, permissionAction: string): boolean => {
    if (!state.user) return false;

    // Implementation would check user permissions against resource/action
    // For now, return basic role-based permissions
    const roleLevel = getRoleLevel(state.user.role);

    switch (resource) {
      case 'admin':
        return roleLevel >= 4; // merchant and above
      case 'analytics':
        return roleLevel >= 1; // all roles
      case 'users':
        return roleLevel >= 2; // branch_admin and above
      default:
        return true;
    }
  };

  const value: AuthContextType = {
    ...state,
    login,
    logout,
    switchScope,
    hasPermission,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Mock authentication function - replace with real API
async function mockAuthenticate(email: string, password: string): Promise<User> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Mock users for testing
  const mockUsers: Record<string, User> = {
    '<EMAIL>': {
      id: 'agent-1',
      name: 'John Agent',
      email: '<EMAIL>',
      role: 'agent',
      organizationId: 'org-1',
      organizationName: 'RPay Network',
      childrenIds: ['merchant-1', 'merchant-2'],
    },
    '<EMAIL>': {
      id: 'merchant-1',
      name: 'Sarah Merchant',
      email: '<EMAIL>',
      role: 'merchant',
      agentId: 'agent-1',
      organizationId: 'merchant-1',
      organizationName: 'TechCorp Payments',
      childrenIds: ['merchant-admin-1', 'merchant-admin-2'],
    },
    '<EMAIL>': {
      id: 'MER-0091',
      name: 'Mike Admin',
      email: '<EMAIL>',
      role: 'merchant_admin',
      agentId: 'agent-1',
      merchantId: 'merchant-1',
      organizationId: 'merchant-admin-1',
      organizationName: 'TechCorp East Region',
      childrenIds: ['branch-admin-1', 'branch-admin-2'],
    },
    '<EMAIL>': {
      id: 'branch-admin-1',
      name: 'Lisa Branch',
      email: '<EMAIL>',
      role: 'branch_admin',
      agentId: 'agent-1',
      merchantId: 'merchant-1',
      merchantAdminId: 'merchant-admin-1',
      organizationId: 'branch-1',
      organizationName: 'Downtown Branch',
      childrenIds: ['teller-1', 'teller-2'],
    },
    '<EMAIL>': {
      id: 'teller-1',
      name: 'Tom Teller',
      email: '<EMAIL>',
      role: 'teller',
      agentId: 'agent-1',
      merchantId: 'merchant-1',
      merchantAdminId: 'merchant-admin-1',
      branchAdminId: 'branch-admin-1',
      organizationId: 'teller-1',
      organizationName: 'Teller Station 1',
    },
  };

  const user = mockUsers[email];
  if (!user || password !== 'password') {
    throw new Error('Invalid credentials');
  }

  return user;
}

function getRoleLevel(role: UserRole): number {
  const levels = {
    agent: 5,
    merchant: 4,
    merchant_admin: 3,
    branch_admin: 2,
    teller: 1,
  };
  return levels[role];
}

function getRoleSpecificDashboard(role: UserRole): string {
  const dashboardRoutes = {
    agent: '/agent/dashboard',
    merchant: '/', // General dashboard at root
    merchant_admin: '/merchant-admin/dashboard',
    branch_admin: '/branch-admin/dashboard',
    teller: '/teller/dashboard',
  };
  return dashboardRoutes[role] || '/';
}
