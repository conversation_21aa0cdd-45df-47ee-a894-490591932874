# RPay Analytics Dashboard - Multi-Tenant RBAC

A comprehensive multi-tenant customer analytics dashboard with hierarchical role-based access control (RBAC), built with Next.js, shadcn/ui, Tailwind CSS, and Recharts.

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Features

### Core Dashboard Features
- Responsive dashboard layout with sidebar navigation and top bar
- Multiple data visualization components using Recharts
- Interactive data tables and KPI cards
- Dark mode support
- Fully responsive design for mobile, tablet, and desktop

### Multi-Tenant & RBAC Features
- **Hierarchical Role System**: Agents → Merchants → Merchant Admins → Branch Admins → Tellers
- **Data Scope Control**: Each role sees aggregated data from their subordinates
- **Role-based Dashboards**: Specialized dashboards with AI insights for each role
- **Entity Switching**: Higher-level users can switch between accessible entities
- **Tenant-aware Data Filtering**: All data is filtered based on user permissions
- **Authentication System**: Secure login with role-based access

### Role-Specific AI Insights
- **Agents**: Merchant activation tracking, performance ranking, transaction volume trends
- **Merchant Admins**: Branch performance ranking, customer behavior analysis, staff efficiency metrics
- **Branch Admins**: Teller performance scorecards, cash flow heatmaps, customer engagement trends
- **Tellers**: Personal sales summaries, customer interaction tips, fraud alerts

## Demo Accounts

The application includes demo accounts for testing different role levels. All accounts use the password: **"password"**

| Email | Role | Access Level | Description |
|-------|------|--------------|-------------|
| <EMAIL> | Agent | Full System | Can see all merchants and their data |
| <EMAIL> | Merchant Admin | Regional | Can see their branch admins and tellers |
| <EMAIL> | Branch Admin | Branch Level | Can see their tellers |
| <EMAIL> | Teller | Individual | Can only see their own data |

## Getting Started

First, install the required dependencies:

```bash
npm install
```

Then, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

### Testing the Multi-Tenant System

1. **Login**: Navigate to the login page and use any of the demo accounts
2. **Role Testing**: Try different accounts to see how data access changes
3. **Entity Switching**: Use the role switcher in the top bar (available for higher-level roles)
4. **Data Scope**: Notice how the tenant header shows current scope and accessible entities
5. **Permissions**: Observe how different roles have access to different features

## Dashboard Structure

### Layout
- Sidebar (Left - Fixed & Collapsible)
- Top Bar (Sticky navigation with search, notifications, and user profile)
- Main Content Area (Dynamic based on selected section)

### Dashboard Sections
- ✅ Overview
- ✅ High-Value Customers
- ✅ Time Between Transactions
- ✅ Transaction Volume
- ✅ Average Transaction Value
- ✅ Returning Customers
- ✅ Spend Patterns
- ✅ Segmentation
- ✅ Growth & Forecasting
- ✅ Alerts (Outliers)

### Components
- KPI Cards with trend indicators
- Data Visualization Charts (Bar, Line, Pie, Scatter, Radar, Heatmap)
- Interactive Data Tables
- Filter Bars
- Theme Switcher (Light/Dark mode)
- Collapsible Sidebar with custom branding (Ctrl/Cmd + B to toggle)

### Theme & Branding
- Primary color: #1D4987 (custom blue)
- Light and dark mode support
- Colored sidebar with clean white navbar
- Subtle gray background for main content area
- White cards with subtle shadows for better contrast
- Consistent color scheme across all components
- Subtle dot pattern background for visual texture

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
