"use client";

import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Building2, Users, TrendingUp, AlertTriangle } from 'lucide-react';
import { useAuth } from '@/lib/auth/context';
import { getRoleDisplayName } from '@/lib/rbac/permissions';
import { entityMetrics } from '@/lib/data/tenant-data';

export function TenantHeader() {
  const { user, currentScope } = useAuth();

  if (!user) return null;

  const metrics = entityMetrics[currentScope.entityId as keyof typeof entityMetrics];

  return (
    <div className="mb-6 space-y-4">
      {/* Scope Information */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">{currentScope.entityName}</h1>
          <div className="flex items-center space-x-2 mt-1">
            <Badge variant="outline">
              {getRoleDisplayName(currentScope.level)}
            </Badge>
            <span className="text-sm text-muted-foreground">
              • Viewing {currentScope.accessibleEntityIds.length} entities
            </span>
            {currentScope.canViewChildren && (
              <Badge variant="secondary" className="text-xs">
                Hierarchical View
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Quick Metrics */}
      {metrics && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4 text-blue-600" />
                <div>
                  <div className="text-2xl font-bold">{metrics.totalCustomers.toLocaleString()}</div>
                  <div className="text-xs text-muted-foreground">Customers</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-4 w-4 text-green-600" />
                <div>
                  <div className="text-2xl font-bold">${metrics.totalRevenue.toLocaleString()}</div>
                  <div className="text-xs text-muted-foreground">Revenue</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Building2 className="h-4 w-4 text-purple-600" />
                <div>
                  <div className="text-2xl font-bold">{metrics.transactionVolume.toLocaleString()}</div>
                  <div className="text-xs text-muted-foreground">Transactions</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4 text-orange-600" />
                <div>
                  <div className="text-2xl font-bold">{metrics.activeAlerts}</div>
                  <div className="text-xs text-muted-foreground">Active Alerts</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
