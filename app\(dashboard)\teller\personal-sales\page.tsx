"use client";

import { useState } from "react";
import {
  DollarSign,
  TrendingUp,
  Activity,
  Target,
  Calendar,
  Award
} from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  <PERSON>C<PERSON>,
  <PERSON>,
  Cell,
  Legend
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatCurrency } from "@/lib/utils/currency";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

// Mock data for personal sales
const personalSalesData = [
  { period: "Week 1", sales: 15680, transactions: 89, avgValue: 176 },
  { period: "Week 2", sales: 18420, transactions: 92, avgValue: 200 },
  { period: "Week 3", sales: 16890, transactions: 87, avgValue: 194 },
  { period: "Week 4", sales: 21340, transactions: 95, avgValue: 225 },
];

const dailySalesData = [
  { day: "Mon", sales: 3200, transactions: 18 },
  { day: "Tue", sales: 4100, transactions: 22 },
  { day: "Wed", sales: 3800, transactions: 19 },
  { day: "Thu", sales: 4500, transactions: 24 },
  { day: "Fri", sales: 5200, transactions: 28 },
  { day: "Sat", sales: 2800, transactions: 15 },
];

const serviceTypeData = [
  { name: "Mobile Top-up", value: 35, revenue: 8500 },
  { name: "Bill Payment", value: 28, revenue: 6800 },
  { name: "Money Transfer", value: 22, revenue: 5300 },
  { name: "Account Services", value: 15, revenue: 3600 },
];

const monthlyGoals = [
  { metric: "Sales Target", target: 75000, achieved: 68240, percentage: 91 },
  { metric: "Transaction Count", target: 350, achieved: 383, percentage: 109 },
  { metric: "Avg. Transaction Value", target: 200, achieved: 198, percentage: 99 },
];

export default function PersonalSalesPage() {
  const { user } = useAuth();

  if (!user || user.role !== 'teller') {
    return <div>Access denied. Teller role required.</div>;
  }

  return (
    <div className="w-full max-w-full overflow-hidden space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Personal Sales Summary</h2>
        <p className="text-muted-foreground">
          Track your individual sales performance and progress toward goals
        </p>
      </div>

      <FilterBar />

      {/* Sales KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="This Month's Sales"
          value={formatCurrency(68240)}
          icon={DollarSign}
          trend={{ value: 12.5, isPositive: true }}
        />
        <KpiCard
          title="Total Transactions"
          value="383"
          icon={Activity}
          trend={{ value: 8.7, isPositive: true }}
        />
        <KpiCard
          title="Avg. Transaction Value"
          value={formatCurrency(198)}
          icon={Target}
          trend={{ value: 3.2, isPositive: true }}
        />
        <KpiCard
          title="Goal Achievement"
          value="91%"
          icon={Award}
          trend={{ value: 5.8, isPositive: true }}
        />
      </div>

      {/* Sales Charts */}
      <div className="grid gap-6 md:grid-cols-2 max-w-full">
        {/* Weekly Sales Trend */}
        <ChartContainer title="Weekly Sales Performance">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={personalSalesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="period" />
              <YAxis />
              <Tooltip formatter={(value, name) => {
                if (name === "sales") return [formatCurrency(Number(value)), "Sales"];
                return [value, name];
              }} />
              <Legend />
              <Line type="monotone" dataKey="sales" stroke={COLORS[0]} strokeWidth={3} name="Sales" />
              <Line type="monotone" dataKey="transactions" stroke={COLORS[1]} strokeWidth={2} name="Transactions" />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>

        {/* Daily Sales This Week */}
        <ChartContainer title="Daily Sales This Week">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dailySalesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="day" />
              <YAxis />
              <Tooltip formatter={(value, name) => {
                if (name === "sales") return [formatCurrency(Number(value)), "Sales"];
                return [value, name];
              }} />
              <Legend />
              <Bar dataKey="sales" fill={COLORS[0]} name="Sales" />
              <Bar dataKey="transactions" fill={COLORS[1]} name="Transactions" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Service Type Performance */}
      <div className="grid gap-6 md:grid-cols-2 max-w-full">
        <ChartContainer title="Sales by Service Type">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={serviceTypeData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, value }) => `${name}: ${value}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {serviceTypeData.map((_, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Revenue by Service Type">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={serviceTypeData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
              <YAxis />
              <Tooltip formatter={(value) => [formatCurrency(Number(value)), "Revenue"]} />
              <Bar dataKey="revenue" fill={COLORS[2]} name="Revenue" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Monthly Goals Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>Monthly Goals Progress</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {monthlyGoals.map((goal, index) => (
              <div key={goal.metric} className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="font-medium">{goal.metric}</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-muted-foreground">
                      {goal.metric.includes('Target') ? formatCurrency(goal.achieved) : goal.achieved} / {goal.metric.includes('Target') ? formatCurrency(goal.target) : goal.target}
                    </span>
                    <Badge variant={goal.percentage >= 100 ? "default" : goal.percentage >= 90 ? "secondary" : "outline"}>
                      {goal.percentage}%
                    </Badge>
                  </div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${goal.percentage >= 100 ? 'bg-green-600' : goal.percentage >= 90 ? 'bg-blue-600' : 'bg-yellow-600'}`}
                    style={{ width: `${Math.min(goal.percentage, 100)}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
