"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Calendar, ChevronDown } from "lucide-react";

export type FilterPeriod = '7days' | '30days' | '90days' | 'custom';

interface MerchantFilterBarProps {
  selectedPeriod?: FilterPeriod;
  onPeriodChange?: (period: FilterPeriod) => void;
  customStartDate?: Date;
  customEndDate?: Date;
  onCustomDateChange?: (startDate: Date, endDate: Date) => void;
  isLoading?: boolean;
}

export function MerchantFilterBar({ 
  selectedPeriod = '30days',
  onPeriodChange,
  customStartDate,
  customEndDate,
  onCustomDateChange,
  isLoading = false
}: MerchantFilterBarProps) {
  const [isCustomDateOpen, setIsCustomDateOpen] = useState(false);

  const periodLabels = {
    '7days': 'Last 7 days',
    '30days': 'Last 30 days', 
    '90days': 'Last 90 days',
    'custom': 'Custom range'
  };

  const handlePeriodSelect = (period: FilterPeriod) => {
    onPeriodChange?.(period);
    if (period === 'custom') {
      setIsCustomDateOpen(true);
    }
  };

  const formatDateRange = () => {
    if (selectedPeriod === 'custom' && customStartDate && customEndDate) {
      return `${customStartDate.toLocaleDateString()} - ${customEndDate.toLocaleDateString()}`;
    }
    return 'Pick a date range';
  };

  return (
    <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div className="flex items-center gap-2">
        {/* Time Period Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="outline" 
              size="sm" 
              className="h-8 w-[140px] justify-between"
              disabled={isLoading}
            >
              <Calendar className="mr-2 h-3.5 w-3.5" />
              {periodLabels[selectedPeriod]}
              <ChevronDown className="ml-2 h-3.5 w-3.5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            <DropdownMenuItem onClick={() => handlePeriodSelect('7days')}>
              Last 7 days
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handlePeriodSelect('30days')}>
              Last 30 days
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handlePeriodSelect('90days')}>
              Last 90 days
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handlePeriodSelect('custom')}>
              Custom range
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Custom Date Range Button */}
        {selectedPeriod === 'custom' && (
          <Button 
            variant="outline" 
            size="sm" 
            className="h-8 w-[180px] justify-start"
            disabled={isLoading}
          >
            <Calendar className="mr-2 h-3.5 w-3.5" />
            {formatDateRange()}
          </Button>
        )}
      </div>

      {/* Loading indicator */}
      {isLoading && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
          Loading data...
        </div>
      )}
    </div>
  );
}

// Helper function to convert FilterPeriod to API parameters
export function getApiParamsFromFilter(
  period: FilterPeriod,
  customStartDate?: Date,
  customEndDate?: Date
): { rangeDays?: number; startDate?: string; endDate?: string } {
  switch (period) {
    case '7days':
      return { rangeDays: 7 };
    case '30days':
      return { rangeDays: 30 };
    case '90days':
      return { rangeDays: 90 };
    case 'custom':
      if (customStartDate && customEndDate) {
        // Format dates for API (MM/DD/YYYY with URL encoding)
        const formatDate = (date: Date): string => {
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          const year = date.getFullYear();
          return `${month}%2F${day}%2F${year}`;
        };
        
        return {
          startDate: formatDate(customStartDate),
          endDate: formatDate(customEndDate)
        };
      }
      return { rangeDays: 30 }; // fallback
    default:
      return { rangeDays: 30 };
  }
}
