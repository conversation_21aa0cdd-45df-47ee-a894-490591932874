import { User, DataScope } from '../auth/types';
import { filterDataByScope } from '../rbac/permissions';
import { 
  tenantCustomers, 
  tenantAlerts, 
  entityMetrics,
  tenantTransactionVolumeData,
  tenantAvgTransactionValueData,
  tenantCustomerSegmentationData,
  TenantCustomer,
  TenantAlert
} from '../data/tenant-data';

export class DataService {
  static getCustomers(user: User, scope: DataScope): TenantCustomer[] {
    return filterDataByScope(tenantCustomers, user, scope);
  }

  static getAlerts(user: User, scope: DataScope): TenantAlert[] {
    return filterDataByScope(tenantAlerts, user, scope);
  }

  static getMetrics(user: User, scope: DataScope) {
    const entityId = scope.entityId;
    const metrics = entityMetrics[entityId as keyof typeof entityMetrics];
    
    if (!metrics) {
      // Aggregate metrics from accessible entities
      const accessibleMetrics = scope.accessibleEntityIds
        .map(id => entityMetrics[id as keyof typeof entityMetrics])
        .filter(Boolean);
      
      if (accessibleMetrics.length === 0) {
        return {
          totalCustomers: 0,
          totalRevenue: 0,
          avgTransactionValue: 0,
          transactionVolume: 0,
          activeAlerts: 0,
        };
      }
      
      return {
        totalCustomers: accessibleMetrics.reduce((sum, m) => sum + m.totalCustomers, 0),
        totalRevenue: accessibleMetrics.reduce((sum, m) => sum + m.totalRevenue, 0),
        avgTransactionValue: accessibleMetrics.reduce((sum, m) => sum + m.avgTransactionValue, 0) / accessibleMetrics.length,
        transactionVolume: accessibleMetrics.reduce((sum, m) => sum + m.transactionVolume, 0),
        activeAlerts: accessibleMetrics.reduce((sum, m) => sum + m.activeAlerts, 0),
      };
    }
    
    return metrics;
  }

  static getTransactionVolumeData(user: User, scope: DataScope) {
    // For now, return the same data but could be filtered/aggregated based on scope
    return tenantTransactionVolumeData;
  }

  static getAvgTransactionValueData(user: User, scope: DataScope) {
    // For now, return the same data but could be filtered/aggregated based on scope
    return tenantAvgTransactionValueData;
  }

  static getCustomerSegmentationData(user: User, scope: DataScope) {
    // For now, return the same data but could be filtered/aggregated based on scope
    return tenantCustomerSegmentationData;
  }

  static getKPIData(user: User, scope: DataScope) {
    const metrics = this.getMetrics(user, scope);
    const customers = this.getCustomers(user, scope);
    
    return {
      totalCustomers: {
        value: metrics.totalCustomers.toLocaleString(),
        trend: { value: 12, isPositive: true }
      },
      totalRevenue: {
        value: `₵${metrics.totalRevenue.toLocaleString()}`,
        trend: { value: 8.2, isPositive: true }
      },
      avgTransactionValue: {
        value: `₵${metrics.avgTransactionValue.toFixed(2)}`,
        trend: { value: 3.1, isPositive: true }
      },
      transactionVolume: {
        value: metrics.transactionVolume.toLocaleString(),
        trend: { value: 5.4, isPositive: true }
      }
    };
  }

  static getEntityHierarchy(user: User): Array<{
    id: string;
    name: string;
    type: string;
    level: number;
    children?: Array<{ id: string; name: string; type: string; level: number }>;
  }> {
    // Mock hierarchy data - in real app, this would come from database
    const hierarchies = {
      'agent-1': {
        id: 'agent-1',
        name: 'RPay Network',
        type: 'agent',
        level: 5,
        children: [
          {
            id: 'merchant-1',
            name: 'TechCorp Payments',
            type: 'merchant',
            level: 4,
            children: [
              {
                id: 'merchant-admin-1',
                name: 'TechCorp East Region',
                type: 'merchant_admin',
                level: 3,
                children: [
                  { id: 'branch-admin-1', name: 'Downtown Branch', type: 'branch_admin', level: 2 },
                  { id: 'branch-admin-2', name: 'Uptown Branch', type: 'branch_admin', level: 2 }
                ]
              },
              {
                id: 'merchant-admin-2',
                name: 'TechCorp West Region',
                type: 'merchant_admin',
                level: 3,
                children: [
                  { id: 'branch-admin-3', name: 'Westside Branch', type: 'branch_admin', level: 2 }
                ]
              }
            ]
          },
          {
            id: 'merchant-2',
            name: 'RetailPay Solutions',
            type: 'merchant',
            level: 4,
            children: [
              {
                id: 'merchant-admin-3',
                name: 'RetailPay North',
                type: 'merchant_admin',
                level: 3,
                children: [
                  { id: 'branch-admin-4', name: 'North Mall Branch', type: 'branch_admin', level: 2 }
                ]
              }
            ]
          }
        ]
      }
    };

    const userHierarchy = hierarchies[user.organizationId as keyof typeof hierarchies];
    return userHierarchy ? [userHierarchy] : [];
  }
}
